Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    gd32f4xx_it.o(i.BusFault_Handler) refers to noretval__2printf.o(.text) for __2printf
    gd32f4xx_it.o(i.HardFault_Handler) refers to noretval__2printf.o(.text) for __2printf
    gd32f4xx_it.o(i.MemManage_Handler) refers to noretval__2printf.o(.text) for __2printf
    gd32f4xx_it.o(i.NMI_Handler) refers to noretval__2printf.o(.text) for __2printf
    gd32f4xx_it.o(i.UsageFault_Handler) refers to noretval__2printf.o(.text) for __2printf
    startup_gd32f450_470.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to port.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to networkinterface.o(i.ENET_IRQHandler) for ENET_IRQHandler
    startup_gd32f450_470.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to __main.o(!!!main) for __main
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(HEAP) for Heap_Mem
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(STACK) for Stack_Mem
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_200m_25m_hxtal) for system_clock_200m_25m_hxtal
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.pcQueueGetName) refers to queue.o(.bss) for xQueueRegistry
    queue.o(i.prvCopyDataFromQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvNotifyQueueSetContainer) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.prvNotifyQueueSetContainer) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvNotifyQueueSetContainer) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to queue.o(i.prvNotifyQueueSetContainer) for prvNotifyQueueSetContainer
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueAddToRegistry) refers to queue.o(.bss) for xQueueRegistry
    queue.o(i.vQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueUnregisterQueue) refers to queue.o(.bss) for xQueueRegistry
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueAddToSet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueAddToSet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueCreateCountingSemaphore) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateSet) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvNotifyQueueSetContainer) for prvNotifyQueueSetContainer
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvIsQueueFull) for prvIsQueueFull
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvNotifyQueueSetContainer) for prvNotifyQueueSetContainer
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueGiveFromISR) refers to queue.o(i.prvNotifyQueueSetContainer) for prvNotifyQueueSetContainer
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueRemoveFromSet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueRemoveFromSet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSelectFromSet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    queue.o(i.xQueueSelectFromSetFromISR) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotifyStateClear) for xTaskGenericNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotifyWait) for xTaskGenericNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotifyStateClear) for xTaskGenericNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotifyWait) for xTaskGenericNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.eTaskGetState) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.eTaskGetState) refers to tasks.o(.bss) for xSuspendedTaskList
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for xTickCount
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for xSuspendedTaskList
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(i.prvInitialiseTaskLists) for prvInitialiseTaskLists
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.bss) for xTasksWaitingTermination
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvCheckTasksWaitingTermination) for prvCheckTasksWaitingTermination
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.prvInitialiseNewTask) refers to aeabi_memset.o(.text) for __aeabi_memset
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvInitialiseTaskLists) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.data) for pxDelayedTaskList
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for pxDelayedTaskList
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for xSuspendedTaskList
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for xSchedulerRunning
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for xNumOfOverflows
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for xYieldPending
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for uxTopReadyPriority
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for xNumOfOverflows
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for xIdleTaskHandle
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for xPendedTicks
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for uxTopReadyPriority
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for xSchedulerRunning
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for xPendingReadyList
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for pxReadyTasksLists
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.data) for xTimerQueue
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.bss) for xActiveTimerList1
    timers.o(i.prvGetNextExpireTime) refers to timers.o(.data) for pxCurrentTimerList
    timers.o(i.prvInitialiseNewTimer) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.prvInitialiseNewTimer) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.prvInsertTimerInActiveList) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvInsertTimerInActiveList) refers to timers.o(.data) for pxOverflowTimerList
    timers.o(i.prvProcessExpiredTimer) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(i.prvReloadTimer) for prvReloadTimer
    timers.o(i.prvProcessExpiredTimer) refers to timers.o(.data) for pxCurrentTimerList
    timers.o(i.prvProcessReceivedCommands) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvReloadTimer) for prvReloadTimer
    timers.o(i.prvProcessReceivedCommands) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvProcessReceivedCommands) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(.data) for xTimerQueue
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvProcessExpiredTimer) for prvProcessExpiredTimer
    timers.o(i.prvProcessTimerOrBlockTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(.data) for pxOverflowTimerList
    timers.o(i.prvReloadTimer) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to timers.o(i.prvSwitchTimerLists) for prvSwitchTimerLists
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for xLastTime
    timers.o(i.prvSwitchTimerLists) refers to timers.o(i.prvProcessExpiredTimer) for prvProcessExpiredTimer
    timers.o(i.prvSwitchTimerLists) refers to timers.o(.data) for pxCurrentTimerList
    timers.o(i.prvTimerTask) refers to timers.o(i.prvGetNextExpireTime) for prvGetNextExpireTime
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessTimerOrBlockTask) for prvProcessTimerOrBlockTask
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessReceivedCommands) for prvProcessReceivedCommands
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.uxTimerGetReloadMode) refers to timers.o(i.xTimerGetReloadMode) for xTimerGetReloadMode
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for xTimerQueue
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommand) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommand) refers to timers.o(.data) for xTimerQueue
    timers.o(i.xTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for xTimerTaskHandle
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerPendFunctionCall) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerPendFunctionCall) refers to timers.o(.data) for xTimerQueue
    timers.o(i.xTimerPendFunctionCallFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerPendFunctionCallFromISR) refers to timers.o(.data) for xTimerQueue
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for ucHeap
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for xStart
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for xStart
    heap_4.o(i.pvPortCalloc) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    heap_4.o(i.pvPortCalloc) refers to rt_memclr.o(.text) for __aeabi_memclr
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for pxEnd
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for xFreeBytesRemaining
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for xStart
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for xFreeBytesRemaining
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for xMinimumEverFreeBytesRemaining
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.SysTick_Handler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEnterCritical) refers to port.o(.data) for uxCriticalNesting
    port.o(i.vPortExitCritical) refers to port.o(.data) for uxCriticalNesting
    port.o(i.vPortSetupTimerInterrupt) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvEnableVFP
    port.o(i.xPortStartScheduler) refers to port.o(.data) for uxCriticalNesting
    freertos_arp.o(i.FreeRTOS_ClearARP) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    freertos_arp.o(i.FreeRTOS_ClearARP) refers to freertos_arp.o(.bss) for xARPCache
    freertos_arp.o(i.FreeRTOS_OutputARPRequest) refers to bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) for pxGetNetworkBufferWithDescriptor
    freertos_arp.o(i.FreeRTOS_OutputARPRequest) refers to freertos_arp.o(i.vARPGenerateRequestPacket) for vARPGenerateRequestPacket
    freertos_arp.o(i.FreeRTOS_OutputARPRequest) refers to freertos_ip_utils.o(i.xIsCallingFromIPTask) for xIsCallingFromIPTask
    freertos_arp.o(i.FreeRTOS_OutputARPRequest) refers to networkinterface.o(i.xNetworkInterfaceOutput) for xNetworkInterfaceOutput
    freertos_arp.o(i.FreeRTOS_OutputARPRequest) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_arp.o(i.FreeRTOS_OutputARPRequest) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_arp.o(i.FreeRTOS_PrintARPCache) refers to freertos_arp.o(.bss) for xARPCache
    freertos_arp.o(i.eARPGetCacheEntry) refers to freertos_ip.o(i.xIsIPv4Multicast) for xIsIPv4Multicast
    freertos_arp.o(i.eARPGetCacheEntry) refers to freertos_ip_utils.o(i.vSetMultiCastIPv4MacAddress) for vSetMultiCastIPv4MacAddress
    freertos_arp.o(i.eARPGetCacheEntry) refers to freertos_arp.o(i.prvCacheLookup) for prvCacheLookup
    freertos_arp.o(i.eARPGetCacheEntry) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_arp.o(i.eARPGetCacheEntry) refers to freertos_ip.o(.constdata) for xBroadcastMACAddress
    freertos_arp.o(i.eARPGetCacheEntry) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_arp.o(i.eARPProcessPacket) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_arp.o(i.eARPProcessPacket) refers to freertos_arp.o(i.FreeRTOS_OutputARPRequest) for FreeRTOS_OutputARPRequest
    freertos_arp.o(i.eARPProcessPacket) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    freertos_arp.o(i.eARPProcessPacket) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_arp.o(i.eARPProcessPacket) refers to memcmp.o(.text) for memcmp
    freertos_arp.o(i.eARPProcessPacket) refers to freertos_arp.o(i.vARPRefreshCacheEntry) for vARPRefreshCacheEntry
    freertos_arp.o(i.eARPProcessPacket) refers to freertos_arp.o(i.vProcessARPPacketReply) for vProcessARPPacketReply
    freertos_arp.o(i.eARPProcessPacket) refers to freertos_arp.o(.data) for uxARPClashCounter
    freertos_arp.o(i.eARPProcessPacket) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_arp.o(i.prvCacheLookup) refers to freertos_arp.o(.bss) for xARPCache
    freertos_arp.o(i.vARPAgeCache) refers to freertos_arp.o(i.FreeRTOS_OutputARPRequest) for FreeRTOS_OutputARPRequest
    freertos_arp.o(i.vARPAgeCache) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    freertos_arp.o(i.vARPAgeCache) refers to freertos_arp.o(.bss) for xARPCache
    freertos_arp.o(i.vARPAgeCache) refers to freertos_arp.o(.data) for xLastGratuitousARPTime
    freertos_arp.o(i.vARPAgeCache) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_arp.o(i.vARPGenerateRequestPacket) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_arp.o(i.vARPGenerateRequestPacket) refers to freertos_arp.o(.constdata) for xDefaultPartARPPacketHeader
    freertos_arp.o(i.vARPGenerateRequestPacket) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_arp.o(i.vARPRefreshCacheEntry) refers to memcmp.o(.text) for memcmp
    freertos_arp.o(i.vARPRefreshCacheEntry) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_arp.o(i.vARPRefreshCacheEntry) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_arp.o(i.vARPRefreshCacheEntry) refers to freertos_arp.o(.bss) for xARPCache
    freertos_arp.o(i.vARPSendGratuitous) refers to freertos_ip.o(i.xSendEventToIPTask) for xSendEventToIPTask
    freertos_arp.o(i.vARPSendGratuitous) refers to freertos_arp.o(.data) for xLastGratuitousARPTime
    freertos_arp.o(i.vProcessARPPacketReply) refers to freertos_arp.o(i.xIsIPInARPCache) for xIsIPInARPCache
    freertos_arp.o(i.vProcessARPPacketReply) refers to freertos_arp.o(i.vARPRefreshCacheEntry) for vARPRefreshCacheEntry
    freertos_arp.o(i.vProcessARPPacketReply) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_arp.o(i.vProcessARPPacketReply) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_arp.o(i.vProcessARPPacketReply) refers to freertos_ip_timers.o(i.vIPSetARPResolutionTimerEnableState) for vIPSetARPResolutionTimerEnableState
    freertos_arp.o(i.vProcessARPPacketReply) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_arp.o(i.vProcessARPPacketReply) refers to freertos_ip.o(.data) for pxARPWaitingNetworkBuffer
    freertos_arp.o(i.xARPWaitResolution) refers to freertos_arp.o(i.eARPGetCacheEntry) for eARPGetCacheEntry
    freertos_arp.o(i.xARPWaitResolution) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_arp.o(i.xARPWaitResolution) refers to freertos_arp.o(i.FreeRTOS_OutputARPRequest) for FreeRTOS_OutputARPRequest
    freertos_arp.o(i.xARPWaitResolution) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    freertos_arp.o(i.xARPWaitResolution) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_arp.o(i.xCheckLoopback) refers to memcmp.o(.text) for memcmp
    freertos_arp.o(i.xCheckLoopback) refers to freertos_ip_utils.o(i.pxDuplicateNetworkBufferWithDescriptor) for pxDuplicateNetworkBufferWithDescriptor
    freertos_arp.o(i.xCheckLoopback) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_arp.o(i.xCheckLoopback) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_arp.o(i.xCheckLoopback) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_arp.o(i.xCheckRequiresARPResolution) refers to freertos_arp.o(i.xIsIPInARPCache) for xIsIPInARPCache
    freertos_arp.o(i.xCheckRequiresARPResolution) refers to freertos_arp.o(i.FreeRTOS_OutputARPRequest) for FreeRTOS_OutputARPRequest
    freertos_arp.o(i.xCheckRequiresARPResolution) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_arp.o(i.xCheckRequiresARPResolution) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_arp.o(i.xIsIPInARPCache) refers to freertos_arp.o(.bss) for xARPCache
    freertos_dns.o(i.FreeRTOS_gethostbyname) refers to freertos_dns.o(i.prvPrepareLookup) for prvPrepareLookup
    freertos_dns.o(i.prvCreateDNSMessage) refers to strcpy.o(.text) for strcpy
    freertos_dns.o(i.prvCreateDNSMessage) refers to freertos_dns.o(.constdata) for xDefaultPartDNSHeader
    freertos_dns.o(i.prvDNSReply) refers to freertos_dns_parser.o(i.DNS_ParseDNSReply) for DNS_ParseDNSReply
    freertos_dns.o(i.prvFillSockAddress) refers to freertos_ip.o(i.FreeRTOS_GetAddressConfiguration) for FreeRTOS_GetAddressConfiguration
    freertos_dns.o(i.prvFillSockAddress) refers to freertos_dns.o(i.llmnr_has_dot) for llmnr_has_dot
    freertos_dns.o(i.prvGetHostByName) refers to freertos_dns_networking.o(i.DNS_CreateSocket) for DNS_CreateSocket
    freertos_dns.o(i.prvGetHostByName) refers to freertos_dns.o(i.prvGetHostByNameOp) for prvGetHostByNameOp
    freertos_dns.o(i.prvGetHostByName) refers to freertos_dns.o(i.prvGetHostByNameOp_WithRetry) for prvGetHostByNameOp_WithRetry
    freertos_dns.o(i.prvGetHostByName) refers to freertos_dns_networking.o(i.DNS_CloseSocket) for DNS_CloseSocket
    freertos_dns.o(i.prvGetHostByNameOp) refers to freertos_dns.o(i.prvFillSockAddress) for prvFillSockAddress
    freertos_dns.o(i.prvGetHostByNameOp) refers to freertos_dns.o(i.prvSendBuffer) for prvSendBuffer
    freertos_dns.o(i.prvGetHostByNameOp) refers to freertos_dns_networking.o(i.DNS_ReadReply) for DNS_ReadReply
    freertos_dns.o(i.prvGetHostByNameOp) refers to freertos_dns.o(i.prvDNSReply) for prvDNSReply
    freertos_dns.o(i.prvGetHostByNameOp) refers to freertos_ip.o(i.FreeRTOS_ReleaseUDPPayloadBuffer) for FreeRTOS_ReleaseUDPPayloadBuffer
    freertos_dns.o(i.prvGetHostByNameOp_WithRetry) refers to freertos_dns.o(i.prvGetHostByNameOp) for prvGetHostByNameOp
    freertos_dns.o(i.prvGetPayloadBuffer) refers to strlen.o(.text) for strlen
    freertos_dns.o(i.prvGetPayloadBuffer) refers to bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) for pxGetNetworkBufferWithDescriptor
    freertos_dns.o(i.prvPrepareLookup) refers to strlen.o(.text) for strlen
    freertos_dns.o(i.prvPrepareLookup) refers to freertos_sockets.o(i.FreeRTOS_inet_addr) for FreeRTOS_inet_addr
    freertos_dns.o(i.prvPrepareLookup) refers to freertos_dns_cache.o(i.FreeRTOS_dnslookup) for FreeRTOS_dnslookup
    freertos_dns.o(i.prvPrepareLookup) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_dns.o(i.prvPrepareLookup) refers to networkinterface.o(i.xApplicationGetRandomNumber) for xApplicationGetRandomNumber
    freertos_dns.o(i.prvPrepareLookup) refers to freertos_dns.o(i.prvGetHostByName) for prvGetHostByName
    freertos_dns.o(i.prvSendBuffer) refers to freertos_dns.o(i.prvGetPayloadBuffer) for prvGetPayloadBuffer
    freertos_dns.o(i.prvSendBuffer) refers to freertos_dns.o(i.prvCreateDNSMessage) for prvCreateDNSMessage
    freertos_dns.o(i.prvSendBuffer) refers to freertos_dns_networking.o(i.DNS_SendRequest) for DNS_SendRequest
    freertos_dns.o(i.prvSendBuffer) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_dns.o(i.ulDNSHandlePacket) refers to freertos_dns_parser.o(i.DNS_ParseDNSReply) for DNS_ParseDNSReply
    freertos_dns.o(i.ulNBNSHandlePacket) refers to freertos_dns_parser.o(i.DNS_TreatNBNS) for DNS_TreatNBNS
    freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache) refers to freertos_dns_cache.o(i.prvFindEntryIndex) for prvFindEntryIndex
    freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache) refers to freertos_dns_cache.o(i.prvGetCacheIPEntry) for prvGetCacheIPEntry
    freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache) refers to freertos_dns_cache.o(i.prvUpdateCacheEntry) for prvUpdateCacheEntry
    freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache) refers to freertos_dns_cache.o(i.prvInsertCacheEntry) for prvInsertCacheEntry
    freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_dns_cache.o(i.FreeRTOS_dns_update) refers to freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache) for FreeRTOS_ProcessDNSCache
    freertos_dns_cache.o(i.FreeRTOS_dnsclear) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    freertos_dns_cache.o(i.FreeRTOS_dnsclear) refers to freertos_dns_cache.o(.bss) for xDNSCache
    freertos_dns_cache.o(i.FreeRTOS_dnsclear) refers to freertos_dns_cache.o(.data) for uxFreeEntry
    freertos_dns_cache.o(i.FreeRTOS_dnslookup) refers to freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache) for FreeRTOS_ProcessDNSCache
    freertos_dns_cache.o(i.prvFindEntryIndex) refers to strcmpv7m.o(.text) for strcmp
    freertos_dns_cache.o(i.prvFindEntryIndex) refers to freertos_dns_cache.o(.bss) for xDNSCache
    freertos_dns_cache.o(i.prvGetCacheIPEntry) refers to freertos_dns_cache.o(.bss) for xDNSCache
    freertos_dns_cache.o(i.prvInsertCacheEntry) refers to strlen.o(.text) for strlen
    freertos_dns_cache.o(i.prvInsertCacheEntry) refers to strcpy.o(.text) for strcpy
    freertos_dns_cache.o(i.prvInsertCacheEntry) refers to freertos_dns_cache.o(.data) for uxFreeEntry
    freertos_dns_cache.o(i.prvInsertCacheEntry) refers to freertos_dns_cache.o(.bss) for xDNSCache
    freertos_dns_cache.o(i.prvUpdateCacheEntry) refers to freertos_dns_cache.o(.bss) for xDNSCache
    freertos_dns_networking.o(i.DNS_CloseSocket) refers to freertos_sockets.o(i.FreeRTOS_closesocket) for FreeRTOS_closesocket
    freertos_dns_networking.o(i.DNS_CreateSocket) refers to freertos_sockets.o(i.FreeRTOS_socket) for FreeRTOS_socket
    freertos_dns_networking.o(i.DNS_CreateSocket) refers to freertos_sockets.o(i.xSocketValid) for xSocketValid
    freertos_dns_networking.o(i.DNS_CreateSocket) refers to freertos_sockets.o(i.FreeRTOS_bind) for FreeRTOS_bind
    freertos_dns_networking.o(i.DNS_CreateSocket) refers to freertos_sockets.o(i.FreeRTOS_closesocket) for FreeRTOS_closesocket
    freertos_dns_networking.o(i.DNS_CreateSocket) refers to freertos_sockets.o(i.FreeRTOS_setsockopt) for FreeRTOS_setsockopt
    freertos_dns_networking.o(i.DNS_ReadReply) refers to freertos_sockets.o(i.FreeRTOS_recvfrom) for FreeRTOS_recvfrom
    freertos_dns_networking.o(i.DNS_SendRequest) refers to freertos_sockets.o(i.FreeRTOS_sendto) for FreeRTOS_sendto
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to freertos_dns_parser.o(i.DNS_ReadNameField) for DNS_ReadNameField
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to freertos_dns_parser.o(i.DNS_SkipNameField) for DNS_SkipNameField
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to freertos_ip_utils.o(i.usChar2u16) for usChar2u16
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to freertos_dns_parser.o(i.parseDNSAnswer) for parseDNSAnswer
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to networkinterface.o(i.xApplicationDNSQueryHook) for xApplicationDNSQueryHook
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to freertos_ip_utils.o(i.pxUDPPayloadBuffer_to_NetworkBuffer) for pxUDPPayloadBuffer_to_NetworkBuffer
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to freertos_ip_utils.o(i.pxDuplicateNetworkBufferWithDescriptor) for pxDuplicateNetworkBufferWithDescriptor
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to freertos_dns_parser.o(i.prepareReplyDNSMessage) for prepareReplyDNSMessage
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to freertos_ip.o(i.vReturnEthernetFrame) for vReturnEthernetFrame
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to bufferallocation_2.o(.constdata) for xBufferAllocFixedSize
    freertos_dns_parser.o(i.DNS_ParseDNSReply) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to freertos_ip_utils.o(i.usChar2u16) for usChar2u16
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to freertos_dns_cache.o(i.FreeRTOS_dns_update) for FreeRTOS_dns_update
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to networkinterface.o(i.xApplicationDNSQueryHook) for xApplicationDNSQueryHook
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to freertos_ip_utils.o(i.pxUDPPayloadBuffer_to_NetworkBuffer) for pxUDPPayloadBuffer_to_NetworkBuffer
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to freertos_ip_utils.o(i.pxDuplicateNetworkBufferWithDescriptor) for pxDuplicateNetworkBufferWithDescriptor
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to freertos_dns_parser.o(i.prepareReplyDNSMessage) for prepareReplyDNSMessage
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to freertos_ip.o(i.vReturnEthernetFrame) for vReturnEthernetFrame
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to bufferallocation_2.o(.constdata) for xBufferAllocFixedSize
    freertos_dns_parser.o(i.DNS_TreatNBNS) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_dns_parser.o(i.parseDNSAnswer) refers to freertos_dns_parser.o(i.DNS_SkipNameField) for DNS_SkipNameField
    freertos_dns_parser.o(i.parseDNSAnswer) refers to freertos_ip_utils.o(i.usChar2u16) for usChar2u16
    freertos_dns_parser.o(i.parseDNSAnswer) refers to freertos_dns_cache.o(i.FreeRTOS_dns_update) for FreeRTOS_dns_update
    freertos_dns_parser.o(i.parseDNSAnswer) refers to freertos_sockets.o(i.FreeRTOS_inet_ntop) for FreeRTOS_inet_ntop
    freertos_dns_parser.o(i.prepareReplyDNSMessage) refers to freertos_ip_utils.o(i.usGenerateChecksum) for usGenerateChecksum
    freertos_dns_parser.o(i.prepareReplyDNSMessage) refers to freertos_ip_utils.o(i.usGenerateProtocolChecksum) for usGenerateProtocolChecksum
    freertos_dns_parser.o(i.prepareReplyDNSMessage) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_dns_parser.o(i.prepareReplyDNSMessage) refers to freertos_ip.o(.data) for usPacketIdentifier
    freertos_icmp.o(i.ProcessICMPPacket) refers to freertos_icmp.o(i.prvProcessICMPEchoRequest) for prvProcessICMPEchoRequest
    freertos_icmp.o(i.prvProcessICMPEchoRequest) refers to freertos_ip_utils.o(i.usGenerateChecksum) for usGenerateChecksum
    freertos_icmp.o(i.prvProcessICMPEchoRequest) refers to freertos_ip_utils.o(i.usGenerateProtocolChecksum) for usGenerateProtocolChecksum
    freertos_icmp.o(i.prvProcessICMPEchoRequest) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.FreeRTOS_GetAddressConfiguration) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.FreeRTOS_GetAddressConfiguration) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_ip.o(i.FreeRTOS_GetDNSServerAddress) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_ip.o(i.FreeRTOS_GetGatewayAddress) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_ip.o(i.FreeRTOS_GetIPAddress) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.FreeRTOS_GetIPTaskHandle) refers to freertos_ip.o(.data) for xIPTaskHandle
    freertos_ip.o(i.FreeRTOS_GetMACAddress) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.FreeRTOS_GetNetmask) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_ip.o(i.FreeRTOS_GetUDPPayloadBuffer) refers to bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) for pxGetNetworkBufferWithDescriptor
    freertos_ip.o(i.FreeRTOS_IPInit) refers to freertos_ip_utils.o(i.vPreCheckConfigs) for vPreCheckConfigs
    freertos_ip.o(i.FreeRTOS_IPInit) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    freertos_ip.o(i.FreeRTOS_IPInit) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    freertos_ip.o(i.FreeRTOS_IPInit) refers to bufferallocation_2.o(i.xNetworkBuffersInitialise) for xNetworkBuffersInitialise
    freertos_ip.o(i.FreeRTOS_IPInit) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    freertos_ip.o(i.FreeRTOS_IPInit) refers to freertos_sockets.o(i.vNetworkSocketsInit) for vNetworkSocketsInit
    freertos_ip.o(i.FreeRTOS_IPInit) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    freertos_ip.o(i.FreeRTOS_IPInit) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_ip.o(i.FreeRTOS_IPInit) refers to queue.o(i.vQueueDelete) for vQueueDelete
    freertos_ip.o(i.FreeRTOS_IPInit) refers to freertos_ip.o(.data) for xNetworkEventQueue
    freertos_ip.o(i.FreeRTOS_IPInit) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_ip.o(i.FreeRTOS_IPInit) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.FreeRTOS_IPInit) refers to freertos_ip.o(i.prvIPTask) for prvIPTask
    freertos_ip.o(i.FreeRTOS_IsNetworkUp) refers to freertos_ip.o(.data) for xNetworkUp
    freertos_ip.o(i.FreeRTOS_NetworkDown) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_ip.o(i.FreeRTOS_NetworkDown) refers to freertos_ip.o(.constdata) for xNetworkDownEvent
    freertos_ip.o(i.FreeRTOS_NetworkDown) refers to freertos_ip.o(.data) for xNetworkDownEventPending
    freertos_ip.o(i.FreeRTOS_NetworkDownFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    freertos_ip.o(i.FreeRTOS_NetworkDownFromISR) refers to freertos_ip.o(.constdata) for xNetworkDownEvent
    freertos_ip.o(i.FreeRTOS_NetworkDownFromISR) refers to freertos_ip.o(.data) for xNetworkEventQueue
    freertos_ip.o(i.FreeRTOS_ReleaseTCPPayloadBuffer) refers to freertos_stream_buffer.o(i.uxStreamBufferGetPtr) for uxStreamBufferGetPtr
    freertos_ip.o(i.FreeRTOS_ReleaseTCPPayloadBuffer) refers to freertos_sockets.o(i.FreeRTOS_recv) for FreeRTOS_recv
    freertos_ip.o(i.FreeRTOS_ReleaseUDPPayloadBuffer) refers to freertos_ip_utils.o(i.pxUDPPayloadBuffer_to_NetworkBuffer) for pxUDPPayloadBuffer_to_NetworkBuffer
    freertos_ip.o(i.FreeRTOS_ReleaseUDPPayloadBuffer) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_ip.o(i.FreeRTOS_SetAddressConfiguration) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.FreeRTOS_SetAddressConfiguration) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_ip.o(i.FreeRTOS_SetGatewayAddress) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_ip.o(i.FreeRTOS_SetIPAddress) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.FreeRTOS_SetNetmask) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_ip.o(i.FreeRTOS_UpdateMACAddress) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.eConsiderFrameForProcessing) refers to memcmp.o(.text) for memcmp
    freertos_ip.o(i.eConsiderFrameForProcessing) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.eConsiderFrameForProcessing) refers to freertos_ip.o(.constdata) for xBroadcastMACAddress
    freertos_ip.o(i.eConsiderFrameForProcessing) refers to freertos_dns.o(.constdata) for xLLMNR_MacAdress
    freertos_ip.o(i.prvAllowIPPacket) refers to memcmp.o(.text) for memcmp
    freertos_ip.o(i.prvAllowIPPacket) refers to freertos_ip.o(i.xIsIPv4Multicast) for xIsIPv4Multicast
    freertos_ip.o(i.prvAllowIPPacket) refers to freertos_ip.o(i.xCheckSizeFields) for xCheckSizeFields
    freertos_ip.o(i.prvAllowIPPacket) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.prvAllowIPPacket) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_ip.o(i.prvAllowIPPacket) refers to freertos_ip.o(.constdata) for xBroadcastMACAddress
    freertos_ip.o(i.prvHandleEthernetPacket) refers to freertos_ip.o(i.prvProcessEthernetPacket) for prvProcessEthernetPacket
    freertos_ip.o(i.prvIPTask) refers to freertos_ip.o(i.FreeRTOS_NetworkDown) for FreeRTOS_NetworkDown
    freertos_ip.o(i.prvIPTask) refers to freertos_ip_timers.o(i.vTCPTimerReload) for vTCPTimerReload
    freertos_ip.o(i.prvIPTask) refers to freertos_ip_timers.o(i.vIPSetARPResolutionTimerEnableState) for vIPSetARPResolutionTimerEnableState
    freertos_ip.o(i.prvIPTask) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_ip.o(i.prvIPTask) refers to freertos_ip.o(i.prvProcessIPEventsAndTimers) for prvProcessIPEventsAndTimers
    freertos_ip.o(i.prvIPTask) refers to freertos_ip.o(.data) for xIPTaskInitialised
    freertos_ip.o(i.prvProcessEthernetPacket) refers to freertos_arp.o(i.eARPProcessPacket) for eARPProcessPacket
    freertos_ip.o(i.prvProcessEthernetPacket) refers to freertos_ip.o(i.prvProcessIPPacket) for prvProcessIPPacket
    freertos_ip.o(i.prvProcessEthernetPacket) refers to freertos_ip.o(i.vReturnEthernetFrame) for vReturnEthernetFrame
    freertos_ip.o(i.prvProcessEthernetPacket) refers to freertos_ip_timers.o(i.vIPTimerStartARPResolution) for vIPTimerStartARPResolution
    freertos_ip.o(i.prvProcessEthernetPacket) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_ip.o(i.prvProcessEthernetPacket) refers to freertos_ip.o(.data) for pxARPWaitingNetworkBuffer
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_ip_timers.o(i.vCheckNetworkTimers) for vCheckNetworkTimers
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_ip_timers.o(i.xCalculateSleepTime) for xCalculateSleepTime
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to queue.o(i.xQueueReceive) for xQueueReceive
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_ip_utils.o(i.prvProcessNetworkDownEvent) for prvProcessNetworkDownEvent
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_ip.o(i.prvHandleEthernetPacket) for prvHandleEthernetPacket
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to networkinterface.o(i.xNetworkInterfaceOutput) for xNetworkInterfaceOutput
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_arp.o(i.vARPAgeCache) for vARPAgeCache
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_sockets.o(i.vSocketBind) for vSocketBind
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_sockets.o(i.vSocketWakeUpUser) for vSocketWakeUpUser
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_sockets.o(i.vSocketClose) for vSocketClose
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) for vProcessGeneratedUDPPacket
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_sockets.o(i.vSocketSelect) for vSocketSelect
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_ip_timers.o(i.vIPSetTCPTimerExpiredState) for vIPSetTCPTimerExpiredState
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_tcp_ip.o(i.xTCPCheckNewClient) for xTCPCheckNewClient
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to event_groups.o(i.vEventGroupDelete) for vEventGroupDelete
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to heap_4.o(i.vPortFree) for vPortFree
    freertos_ip.o(i.prvProcessIPEventsAndTimers) refers to freertos_ip.o(.data) for xNetworkEventQueue
    freertos_ip.o(i.prvProcessIPPacket) refers to freertos_ip.o(i.prvAllowIPPacket) for prvAllowIPPacket
    freertos_ip.o(i.prvProcessIPPacket) refers to rt_memmove_v6.o(.text) for __aeabi_memmove
    freertos_ip.o(i.prvProcessIPPacket) refers to freertos_arp.o(i.xCheckRequiresARPResolution) for xCheckRequiresARPResolution
    freertos_ip.o(i.prvProcessIPPacket) refers to freertos_arp.o(i.vARPRefreshCacheEntry) for vARPRefreshCacheEntry
    freertos_ip.o(i.prvProcessIPPacket) refers to freertos_icmp.o(i.ProcessICMPPacket) for ProcessICMPPacket
    freertos_ip.o(i.prvProcessIPPacket) refers to freertos_udp_ip.o(i.xProcessReceivedUDPPacket) for xProcessReceivedUDPPacket
    freertos_ip.o(i.prvProcessIPPacket) refers to freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) for xProcessReceivedTCPPacket
    freertos_ip.o(i.prvProcessIPPacket) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.prvProcessIPPacket) refers to freertos_ip.o(.data) for xProcessedTCPMessage
    freertos_ip.o(i.vIPNetworkUpCalls) refers to networkinterface.o(i.vApplicationIPNetworkEventHook) for vApplicationIPNetworkEventHook
    freertos_ip.o(i.vIPNetworkUpCalls) refers to freertos_ip_timers.o(i.vARPTimerReload) for vARPTimerReload
    freertos_ip.o(i.vIPNetworkUpCalls) refers to freertos_ip.o(.data) for xNetworkUp
    freertos_ip.o(i.vReturnEthernetFrame) refers to networkinterface.o(i.xNetworkInterfaceOutput) for xNetworkInterfaceOutput
    freertos_ip.o(i.vReturnEthernetFrame) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_ip.o(i.xIPIsNetworkTaskReady) refers to freertos_ip.o(.data) for xIPTaskInitialised
    freertos_ip.o(i.xIsNetworkDownEventPending) refers to freertos_ip.o(.data) for xNetworkDownEventPending
    freertos_ip.o(i.xSendEventStructToIPTask) refers to freertos_ip.o(i.xIPIsNetworkTaskReady) for xIPIsNetworkTaskReady
    freertos_ip.o(i.xSendEventStructToIPTask) refers to freertos_ip_timers.o(i.vIPSetTCPTimerExpiredState) for vIPSetTCPTimerExpiredState
    freertos_ip.o(i.xSendEventStructToIPTask) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    freertos_ip.o(i.xSendEventStructToIPTask) refers to freertos_ip_utils.o(i.xIsCallingFromIPTask) for xIsCallingFromIPTask
    freertos_ip.o(i.xSendEventStructToIPTask) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    freertos_ip.o(i.xSendEventStructToIPTask) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_ip.o(i.xSendEventStructToIPTask) refers to freertos_ip.o(.data) for xNetworkEventQueue
    freertos_ip.o(i.xSendEventToIPTask) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_ip_timers.o(i.prvIPTimerCheck) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_ip_timers.o(i.prvIPTimerCheck) refers to freertos_ip_timers.o(i.prvIPTimerStart) for prvIPTimerStart
    freertos_ip_timers.o(i.prvIPTimerReload) refers to freertos_ip_timers.o(i.prvIPTimerStart) for prvIPTimerStart
    freertos_ip_timers.o(i.prvIPTimerStart) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_ip_timers.o(i.vARPTimerReload) refers to freertos_ip_timers.o(i.prvIPTimerReload) for prvIPTimerReload
    freertos_ip_timers.o(i.vARPTimerReload) refers to freertos_ip_timers.o(.bss) for xARPTimer
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to freertos_ip_timers.o(i.prvIPTimerCheck) for prvIPTimerCheck
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to freertos_ip.o(i.xSendEventToIPTask) for xSendEventToIPTask
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to freertos_ip_timers.o(i.vIPSetARPResolutionTimerEnableState) for vIPSetARPResolutionTimerEnableState
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to freertos_sockets.o(i.xTCPTimerCheck) for xTCPTimerCheck
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to freertos_ip_timers.o(i.prvIPTimerStart) for prvIPTimerStart
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to freertos_tcp_ip.o(i.vSocketCloseNextTime) for vSocketCloseNextTime
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to freertos_tcp_ip.o(i.vSocketListenNextTime) for vSocketListenNextTime
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to freertos_ip_timers.o(.bss) for xARPTimer
    freertos_ip_timers.o(i.vCheckNetworkTimers) refers to freertos_ip.o(.data) for pxARPWaitingNetworkBuffer
    freertos_ip_timers.o(i.vIPSetARPResolutionTimerEnableState) refers to freertos_ip_timers.o(.bss) for xARPResolutionTimer
    freertos_ip_timers.o(i.vIPSetARPTimerEnableState) refers to freertos_ip_timers.o(.bss) for xARPTimer
    freertos_ip_timers.o(i.vIPSetTCPTimerExpiredState) refers to freertos_ip_timers.o(.bss) for xTCPTimer
    freertos_ip_timers.o(i.vIPTimerStartARPResolution) refers to freertos_ip_timers.o(i.prvIPTimerStart) for prvIPTimerStart
    freertos_ip_timers.o(i.vIPTimerStartARPResolution) refers to freertos_ip_timers.o(.bss) for xARPResolutionTimer
    freertos_ip_timers.o(i.vTCPTimerReload) refers to freertos_ip_timers.o(i.prvIPTimerReload) for prvIPTimerReload
    freertos_ip_timers.o(i.vTCPTimerReload) refers to freertos_ip_timers.o(.bss) for xTCPTimer
    freertos_ip_timers.o(i.xCalculateSleepTime) refers to freertos_ip_timers.o(.bss) for xARPTimer
    freertos_ip_utils.o(i.FreeRTOS_strerror_r) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos_ip_utils.o(i.FreeRTOS_strerror_r) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    freertos_ip_utils.o(i.FreeRTOS_strerror_r) refers to _printf_dec.o(.text) for _printf_int_dec
    freertos_ip_utils.o(i.FreeRTOS_strerror_r) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    freertos_ip_utils.o(i.FreeRTOS_strerror_r) refers to _printf_str.o(.text) for _printf_str
    freertos_ip_utils.o(i.FreeRTOS_strerror_r) refers to noretval__2snprintf.o(.text) for __2snprintf
    freertos_ip_utils.o(i.prvProcessNetworkDownEvent) refers to freertos_ip_timers.o(i.vIPSetARPTimerEnableState) for vIPSetARPTimerEnableState
    freertos_ip_utils.o(i.prvProcessNetworkDownEvent) refers to networkinterface.o(i.vApplicationIPNetworkEventHook) for vApplicationIPNetworkEventHook
    freertos_ip_utils.o(i.prvProcessNetworkDownEvent) refers to freertos_arp.o(i.FreeRTOS_ClearARP) for FreeRTOS_ClearARP
    freertos_ip_utils.o(i.prvProcessNetworkDownEvent) refers to networkinterface.o(i.xNetworkInterfaceInitialise) for xNetworkInterfaceInitialise
    freertos_ip_utils.o(i.prvProcessNetworkDownEvent) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    freertos_ip_utils.o(i.prvProcessNetworkDownEvent) refers to freertos_ip.o(i.FreeRTOS_NetworkDown) for FreeRTOS_NetworkDown
    freertos_ip_utils.o(i.prvProcessNetworkDownEvent) refers to freertos_ip.o(i.vIPNetworkUpCalls) for vIPNetworkUpCalls
    freertos_ip_utils.o(i.prvProcessNetworkDownEvent) refers to freertos_ip_utils.o(.data) for xCallEventHook
    freertos_ip_utils.o(i.pxDuplicateNetworkBufferWithDescriptor) refers to bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) for pxGetNetworkBufferWithDescriptor
    freertos_ip_utils.o(i.pxDuplicateNetworkBufferWithDescriptor) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_ip_utils.o(i.pxUDPPayloadBuffer_to_NetworkBuffer) refers to freertos_ip_utils.o(i.prvPacketBuffer_to_NetworkBuffer) for prvPacketBuffer_to_NetworkBuffer
    freertos_ip_utils.o(i.usGenerateProtocolChecksum) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_ip_utils.o(i.usGenerateProtocolChecksum) refers to freertos_ip_utils.o(i.usGenerateChecksum) for usGenerateChecksum
    freertos_ip_utils.o(i.usGenerateProtocolChecksum) refers to freertos_ip_utils.o(.conststring) for .conststring
    freertos_ip_utils.o(i.xIsCallingFromIPTask) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    freertos_ip_utils.o(i.xIsCallingFromIPTask) refers to freertos_ip.o(i.FreeRTOS_GetIPTaskHandle) for FreeRTOS_GetIPTaskHandle
    freertos_sockets.o(i.FreeRTOS_CreateSocketSet) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    freertos_sockets.o(i.FreeRTOS_CreateSocketSet) refers to event_groups.o(i.xEventGroupCreate) for xEventGroupCreate
    freertos_sockets.o(i.FreeRTOS_CreateSocketSet) refers to heap_4.o(i.vPortFree) for vPortFree
    freertos_sockets.o(i.FreeRTOS_DeleteSocketSet) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_sockets.o(i.FreeRTOS_EUI48_pton) refers to strlen.o(.text) for strlen
    freertos_sockets.o(i.FreeRTOS_EUI48_pton) refers to freertos_sockets.o(i.ucASCIIToHex) for ucASCIIToHex
    freertos_sockets.o(i.FreeRTOS_FD_SET) refers to freertos_sockets.o(i.prvFindSelectedSocket) for prvFindSelectedSocket
    freertos_sockets.o(i.FreeRTOS_GetLocalAddress) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_sockets.o(i.FreeRTOS_accept) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.FreeRTOS_accept) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    freertos_sockets.o(i.FreeRTOS_accept) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    freertos_sockets.o(i.FreeRTOS_accept) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_sockets.o(i.FreeRTOS_accept) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_sockets.o(i.FreeRTOS_accept) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_sockets.o(i.FreeRTOS_accept) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    freertos_sockets.o(i.FreeRTOS_bind) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.FreeRTOS_bind) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_sockets.o(i.FreeRTOS_bind) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    freertos_sockets.o(i.FreeRTOS_closesocket) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_sockets.o(i.FreeRTOS_closesocket) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.FreeRTOS_connect) refers to freertos_sockets.o(i.prvTCPConnectStart) for prvTCPConnectStart
    freertos_sockets.o(i.FreeRTOS_connect) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_sockets.o(i.FreeRTOS_connect) refers to freertos_sockets.o(i.FreeRTOS_issocketconnected) for FreeRTOS_issocketconnected
    freertos_sockets.o(i.FreeRTOS_connect) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_sockets.o(i.FreeRTOS_connect) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    freertos_sockets.o(i.FreeRTOS_connect) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.FreeRTOS_get_rx_buf) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.FreeRTOS_get_tx_head) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.FreeRTOS_get_tx_head) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSpace) for uxStreamBufferGetSpace
    freertos_sockets.o(i.FreeRTOS_inet_addr) refers to freertos_sockets.o(i.FreeRTOS_inet_pton4) for FreeRTOS_inet_pton4
    freertos_sockets.o(i.FreeRTOS_inet_ntop) refers to freertos_sockets.o(i.FreeRTOS_inet_ntop4) for FreeRTOS_inet_ntop4
    freertos_sockets.o(i.FreeRTOS_inet_ntop4) refers to freertos_sockets.o(i.FreeRTOS_inet_ntoa) for FreeRTOS_inet_ntoa
    freertos_sockets.o(i.FreeRTOS_inet_pton) refers to freertos_sockets.o(i.FreeRTOS_inet_pton4) for FreeRTOS_inet_pton4
    freertos_sockets.o(i.FreeRTOS_listen) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.FreeRTOS_listen) refers to freertos_ip_utils.o(i.FreeRTOS_min_int32) for FreeRTOS_min_int32
    freertos_sockets.o(i.FreeRTOS_listen) refers to freertos_stream_buffer.o(i.vStreamBufferClear) for vStreamBufferClear
    freertos_sockets.o(i.FreeRTOS_listen) refers to rt_memclr.o(.text) for __aeabi_memclr
    freertos_sockets.o(i.FreeRTOS_listen) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    freertos_sockets.o(i.FreeRTOS_listen) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_sockets.o(i.FreeRTOS_maywrite) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSpace) for uxStreamBufferGetSpace
    freertos_sockets.o(i.FreeRTOS_netstat) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_sockets.o(i.FreeRTOS_recv) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.FreeRTOS_recv) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSize) for uxStreamBufferGetSize
    freertos_sockets.o(i.FreeRTOS_recv) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_sockets.o(i.FreeRTOS_recv) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_sockets.o(i.FreeRTOS_recv) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    freertos_sockets.o(i.FreeRTOS_recv) refers to freertos_stream_buffer.o(i.uxStreamBufferGet) for uxStreamBufferGet
    freertos_sockets.o(i.FreeRTOS_recv) refers to freertos_stream_buffer.o(i.uxStreamBufferFrontSpace) for uxStreamBufferFrontSpace
    freertos_sockets.o(i.FreeRTOS_recv) refers to freertos_ip.o(i.xSendEventToIPTask) for xSendEventToIPTask
    freertos_sockets.o(i.FreeRTOS_recv) refers to freertos_stream_buffer.o(i.uxStreamBufferGetPtr) for uxStreamBufferGetPtr
    freertos_sockets.o(i.FreeRTOS_recvfrom) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.FreeRTOS_recvfrom) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_sockets.o(i.FreeRTOS_recvfrom) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    freertos_sockets.o(i.FreeRTOS_recvfrom) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_sockets.o(i.FreeRTOS_recvfrom) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    freertos_sockets.o(i.FreeRTOS_recvfrom) refers to list.o(i.uxListRemove) for uxListRemove
    freertos_sockets.o(i.FreeRTOS_recvfrom) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    freertos_sockets.o(i.FreeRTOS_recvfrom) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_sockets.o(i.FreeRTOS_recvfrom) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_sockets.o(i.FreeRTOS_rx_size) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSize) for uxStreamBufferGetSize
    freertos_sockets.o(i.FreeRTOS_select) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_sockets.o(i.FreeRTOS_select) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    freertos_sockets.o(i.FreeRTOS_select) refers to freertos_sockets.o(i.prvFindSelectedSocket) for prvFindSelectedSocket
    freertos_sockets.o(i.FreeRTOS_select) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    freertos_sockets.o(i.FreeRTOS_select) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_sockets.o(i.FreeRTOS_send) refers to freertos_sockets.o(i.prvTCPSendCheck) for prvTCPSendCheck
    freertos_sockets.o(i.FreeRTOS_send) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSpace) for uxStreamBufferGetSpace
    freertos_sockets.o(i.FreeRTOS_send) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    freertos_sockets.o(i.FreeRTOS_send) refers to freertos_stream_buffer.o(i.uxStreamBufferAdd) for uxStreamBufferAdd
    freertos_sockets.o(i.FreeRTOS_send) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    freertos_sockets.o(i.FreeRTOS_send) refers to freertos_ip_utils.o(i.xIsCallingFromIPTask) for xIsCallingFromIPTask
    freertos_sockets.o(i.FreeRTOS_send) refers to freertos_ip.o(i.xSendEventToIPTask) for xSendEventToIPTask
    freertos_sockets.o(i.FreeRTOS_send) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_sockets.o(i.FreeRTOS_send) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_sockets.o(i.FreeRTOS_send) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    freertos_sockets.o(i.FreeRTOS_send) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.FreeRTOS_sendto) refers to freertos_sockets.o(i.prvMakeSureSocketIsBound) for prvMakeSureSocketIsBound
    freertos_sockets.o(i.FreeRTOS_sendto) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    freertos_sockets.o(i.FreeRTOS_sendto) refers to bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) for pxGetNetworkBufferWithDescriptor
    freertos_sockets.o(i.FreeRTOS_sendto) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_sockets.o(i.FreeRTOS_sendto) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    freertos_sockets.o(i.FreeRTOS_sendto) refers to freertos_ip_utils.o(i.pxUDPPayloadBuffer_to_NetworkBuffer) for pxUDPPayloadBuffer_to_NetworkBuffer
    freertos_sockets.o(i.FreeRTOS_sendto) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_sockets.o(i.FreeRTOS_sendto) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_sockets.o(i.FreeRTOS_sendto) refers to freertos_sockets.o(.constdata) for .constdata
    freertos_sockets.o(i.FreeRTOS_setsockopt) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.FreeRTOS_setsockopt) refers to freertos_sockets.o(i.prvSockopt_so_buffer) for prvSockopt_so_buffer
    freertos_sockets.o(i.FreeRTOS_setsockopt) refers to freertos_sockets.o(i.FreeRTOS_tx_size) for FreeRTOS_tx_size
    freertos_sockets.o(i.FreeRTOS_setsockopt) refers to freertos_ip.o(i.xSendEventToIPTask) for xSendEventToIPTask
    freertos_sockets.o(i.FreeRTOS_shutdown) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.FreeRTOS_shutdown) refers to freertos_ip.o(i.xSendEventToIPTask) for xSendEventToIPTask
    freertos_sockets.o(i.FreeRTOS_socket) refers to freertos_sockets.o(i.prvDetermineSocketSize) for prvDetermineSocketSize
    freertos_sockets.o(i.FreeRTOS_socket) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    freertos_sockets.o(i.FreeRTOS_socket) refers to event_groups.o(i.xEventGroupCreate) for xEventGroupCreate
    freertos_sockets.o(i.FreeRTOS_socket) refers to heap_4.o(i.vPortFree) for vPortFree
    freertos_sockets.o(i.FreeRTOS_socket) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    freertos_sockets.o(i.FreeRTOS_socket) refers to list.o(i.vListInitialise) for vListInitialise
    freertos_sockets.o(i.FreeRTOS_socket) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    freertos_sockets.o(i.FreeRTOS_socket) refers to freertos_ip_utils.o(i.FreeRTOS_round_up) for FreeRTOS_round_up
    freertos_sockets.o(i.FreeRTOS_socket) refers to freertos_ip_utils.o(i.FreeRTOS_max_uint32) for FreeRTOS_max_uint32
    freertos_sockets.o(i.FreeRTOS_tx_size) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSize) for uxStreamBufferGetSize
    freertos_sockets.o(i.FreeRTOS_tx_space) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSpace) for uxStreamBufferGetSpace
    freertos_sockets.o(i.lTCPAddRxdata) refers to freertos_sockets.o(i.prvTCPCreateStream) for prvTCPCreateStream
    freertos_sockets.o(i.lTCPAddRxdata) refers to freertos_stream_buffer.o(i.uxStreamBufferAdd) for uxStreamBufferAdd
    freertos_sockets.o(i.lTCPAddRxdata) refers to freertos_stream_buffer.o(i.uxStreamBufferFrontSpace) for uxStreamBufferFrontSpace
    freertos_sockets.o(i.lTCPAddRxdata) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.lTCPAddRxdata) refers to freertos_ip.o(i.xSendEventToIPTask) for xSendEventToIPTask
    freertos_sockets.o(i.lTCPAddRxdata) refers to freertos_sockets.o(.conststring) for .conststring
    freertos_sockets.o(i.prvDetermineSocketSize) refers to freertos_ip.o(i.xIPIsNetworkTaskReady) for xIPIsNetworkTaskReady
    freertos_sockets.o(i.prvFindSelectedSocket) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    freertos_sockets.o(i.prvFindSelectedSocket) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    freertos_sockets.o(i.prvFindSelectedSocket) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.prvFindSelectedSocket) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    freertos_sockets.o(i.prvGetPrivatePortNumber) refers to networkinterface.o(i.xApplicationGetRandomNumber) for xApplicationGetRandomNumber
    freertos_sockets.o(i.prvGetPrivatePortNumber) refers to freertos_sockets.o(i.pxListFindListItemWithValue) for pxListFindListItemWithValue
    freertos_sockets.o(i.prvGetPrivatePortNumber) refers to freertos_sockets.o(.bss) for xBoundTCPSocketsList
    freertos_sockets.o(i.prvMakeSureSocketIsBound) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.prvMakeSureSocketIsBound) refers to freertos_sockets.o(i.FreeRTOS_bind) for FreeRTOS_bind
    freertos_sockets.o(i.prvSockopt_so_buffer) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.prvSockopt_so_buffer) refers to freertos_ip_utils.o(i.FreeRTOS_round_up) for FreeRTOS_round_up
    freertos_sockets.o(i.prvTCPConnectStart) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.prvTCPConnectStart) refers to freertos_sockets.o(i.FreeRTOS_issocketconnected) for FreeRTOS_issocketconnected
    freertos_sockets.o(i.prvTCPConnectStart) refers to freertos_sockets.o(i.FreeRTOS_bind) for FreeRTOS_bind
    freertos_sockets.o(i.prvTCPConnectStart) refers to freertos_sockets.o(i.bMayConnect) for bMayConnect
    freertos_sockets.o(i.prvTCPConnectStart) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.prvTCPConnectStart) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_sockets.o(i.prvTCPConnectStart) refers to freertos_ip.o(i.xSendEventToIPTask) for xSendEventToIPTask
    freertos_sockets.o(i.prvTCPCreateStream) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    freertos_sockets.o(i.prvTCPCreateStream) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.prvTCPCreateStream) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_sockets.o(i.prvTCPCreateStream) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    freertos_sockets.o(i.prvTCPCreateStream) refers to freertos_sockets.o(.constdata) for xPercTable
    freertos_sockets.o(i.prvTCPCreateStream) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_sockets.o(i.prvTCPSendCheck) refers to freertos_sockets.o(i.prvValidSocket) for prvValidSocket
    freertos_sockets.o(i.prvTCPSendCheck) refers to freertos_sockets.o(i.prvTCPCreateStream) for prvTCPCreateStream
    freertos_sockets.o(i.prvTCPSetSocketCount) refers to freertos_sockets.o(i.vSocketClose) for vSocketClose
    freertos_sockets.o(i.prvTCPSetSocketCount) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.prvTCPSetSocketCount) refers to freertos_sockets.o(.bss) for xBoundTCPSocketsList
    freertos_sockets.o(i.pxListFindListItemWithValue) refers to freertos_ip.o(i.xIPIsNetworkTaskReady) for xIPIsNetworkTaskReady
    freertos_sockets.o(i.pxTCPSocketLookup) refers to freertos_sockets.o(.bss) for xBoundTCPSocketsList
    freertos_sockets.o(i.pxUDPSocketLookup) refers to freertos_sockets.o(i.pxListFindListItemWithValue) for pxListFindListItemWithValue
    freertos_sockets.o(i.pxUDPSocketLookup) refers to freertos_sockets.o(.bss) for xBoundUDPSocketsList
    freertos_sockets.o(i.vNetworkSocketsInit) refers to list.o(i.vListInitialise) for vListInitialise
    freertos_sockets.o(i.vNetworkSocketsInit) refers to freertos_sockets.o(.bss) for xBoundUDPSocketsList
    freertos_sockets.o(i.vSocketBind) refers to freertos_sockets.o(i.prvGetPrivatePortNumber) for prvGetPrivatePortNumber
    freertos_sockets.o(i.vSocketBind) refers to freertos_sockets.o(i.pxListFindListItemWithValue) for pxListFindListItemWithValue
    freertos_sockets.o(i.vSocketBind) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.vSocketBind) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    freertos_sockets.o(i.vSocketBind) refers to freertos_sockets.o(.bss) for xBoundTCPSocketsList
    freertos_sockets.o(i.vSocketClose) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_sockets.o(i.vSocketClose) refers to freertos_tcp_win.o(i.vTCPWindowDestroy) for vTCPWindowDestroy
    freertos_sockets.o(i.vSocketClose) refers to heap_4.o(i.vPortFree) for vPortFree
    freertos_sockets.o(i.vSocketClose) refers to freertos_sockets.o(i.prvTCPSetSocketCount) for prvTCPSetSocketCount
    freertos_sockets.o(i.vSocketClose) refers to list.o(i.uxListRemove) for uxListRemove
    freertos_sockets.o(i.vSocketClose) refers to event_groups.o(i.vEventGroupDelete) for vEventGroupDelete
    freertos_sockets.o(i.vSocketClose) refers to bufferallocation_2.o(i.uxGetNumberOfFreeNetworkBuffers) for uxGetNumberOfFreeNetworkBuffers
    freertos_sockets.o(i.vSocketClose) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_sockets.o(i.vSocketClose) refers to freertos_sockets.o(.bss) for xBoundTCPSocketsList
    freertos_sockets.o(i.vSocketSelect) refers to freertos_sockets.o(i.FreeRTOS_rx_size) for FreeRTOS_rx_size
    freertos_sockets.o(i.vSocketSelect) refers to freertos_sockets.o(i.FreeRTOS_tx_space) for FreeRTOS_tx_space
    freertos_sockets.o(i.vSocketSelect) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    freertos_sockets.o(i.vSocketSelect) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    freertos_sockets.o(i.vSocketSelect) refers to freertos_sockets.o(.bss) for xBoundUDPSocketsList
    freertos_sockets.o(i.vSocketWakeUpUser) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    freertos_sockets.o(i.xTCPTimerCheck) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    freertos_sockets.o(i.xTCPTimerCheck) refers to freertos_tcp_ip.o(i.xTCPSocketCheck) for xTCPSocketCheck
    freertos_sockets.o(i.xTCPTimerCheck) refers to freertos_sockets.o(i.vSocketWakeUpUser) for vSocketWakeUpUser
    freertos_sockets.o(i.xTCPTimerCheck) refers to freertos_sockets.o(.data) for xLastTime
    freertos_sockets.o(i.xTCPTimerCheck) refers to freertos_sockets.o(.bss) for xBoundTCPSocketsList
    freertos_stream_buffer.o(i.uxStreamBufferAdd) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSpace) for uxStreamBufferGetSpace
    freertos_stream_buffer.o(i.uxStreamBufferAdd) refers to freertos_ip_utils.o(i.FreeRTOS_min_size_t) for FreeRTOS_min_size_t
    freertos_stream_buffer.o(i.uxStreamBufferAdd) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_stream_buffer.o(i.uxStreamBufferAdd) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    freertos_stream_buffer.o(i.uxStreamBufferAdd) refers to freertos_stream_buffer.o(i.xStreamBufferLessThenEqual) for xStreamBufferLessThenEqual
    freertos_stream_buffer.o(i.uxStreamBufferAdd) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    freertos_stream_buffer.o(i.uxStreamBufferFrontSpace) refers to freertos_stream_buffer.o(i.uxStreamBufferSpace) for uxStreamBufferSpace
    freertos_stream_buffer.o(i.uxStreamBufferGet) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSize) for uxStreamBufferGetSize
    freertos_stream_buffer.o(i.uxStreamBufferGet) refers to freertos_ip_utils.o(i.FreeRTOS_min_size_t) for FreeRTOS_min_size_t
    freertos_stream_buffer.o(i.uxStreamBufferGet) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_stream_buffer.o(i.uxStreamBufferGetPtr) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSize) for uxStreamBufferGetSize
    freertos_stream_buffer.o(i.uxStreamBufferGetPtr) refers to freertos_ip_utils.o(i.FreeRTOS_min_size_t) for FreeRTOS_min_size_t
    freertos_stream_buffer.o(i.uxStreamBufferGetSize) refers to freertos_stream_buffer.o(i.uxStreamBufferDistance) for uxStreamBufferDistance
    freertos_stream_buffer.o(i.uxStreamBufferGetSpace) refers to freertos_stream_buffer.o(i.uxStreamBufferSpace) for uxStreamBufferSpace
    freertos_stream_buffer.o(i.uxStreamBufferMidSpace) refers to freertos_stream_buffer.o(i.uxStreamBufferDistance) for uxStreamBufferDistance
    freertos_stream_buffer.o(i.vStreamBufferMoveMid) refers to freertos_stream_buffer.o(i.uxStreamBufferMidSpace) for uxStreamBufferMidSpace
    freertos_tcp_ip.o(i.prvTCPNextTimeout) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_ip.o(i.prvTCPNextTimeout) refers to freertos_tcp_win.o(i.xTCPWindowTxHasData) for xTCPWindowTxHasData
    freertos_tcp_ip.o(i.prvTCPTouchSocket) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    freertos_tcp_ip.o(i.vSocketCloseNextTime) refers to freertos_sockets.o(i.vSocketClose) for vSocketClose
    freertos_tcp_ip.o(i.vSocketCloseNextTime) refers to freertos_tcp_ip.o(.data) for xSocketToClose
    freertos_tcp_ip.o(i.vSocketListenNextTime) refers to freertos_sockets.o(i.FreeRTOS_listen) for FreeRTOS_listen
    freertos_tcp_ip.o(i.vSocketListenNextTime) refers to freertos_tcp_ip.o(.data) for xSocketToListen
    freertos_tcp_ip.o(i.vTCPStateChange) refers to freertos_tcp_state_handling.o(i.FreeRTOS_GetTCPStateName) for FreeRTOS_GetTCPStateName
    freertos_tcp_ip.o(i.vTCPStateChange) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_ip.o(i.vTCPStateChange) refers to freertos_tcp_state_handling.o(i.prvTCPSocketIsActive) for prvTCPSocketIsActive
    freertos_tcp_ip.o(i.vTCPStateChange) refers to freertos_tcp_ip.o(i.vSocketCloseNextTime) for vSocketCloseNextTime
    freertos_tcp_ip.o(i.vTCPStateChange) refers to freertos_tcp_ip.o(i.vSocketListenNextTime) for vSocketListenNextTime
    freertos_tcp_ip.o(i.vTCPStateChange) refers to freertos_tcp_ip.o(i.prvTCPTouchSocket) for prvTCPTouchSocket
    freertos_tcp_ip.o(i.vTCPStateChange) refers to freertos_sockets.o(i.vSocketWakeUpUser) for vSocketWakeUpUser
    freertos_tcp_ip.o(i.vTCPStateChange) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_sockets.o(i.pxTCPSocketLookup) for pxTCPSocketLookup
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_state_handling.o(i.prvTCPSocketIsActive) for prvTCPSocketIsActive
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_transmission.o(i.prvTCPSendReset) for prvTCPSendReset
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_utils.o(i.prvTCPFlagMeaning) for prvTCPFlagMeaning
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_state_handling.o(i.prvHandleListen) for prvHandleListen
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_win.o(i.xSequenceGreaterThan) for xSequenceGreaterThan
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_win.o(i.xSequenceLessThan) for xSequenceLessThan
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_transmission.o(i.prvTCPSendChallengeAck) for prvTCPSendChallengeAck
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_ip.o(i.prvTCPTouchSocket) for prvTCPTouchSocket
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_reception.o(i.prvCheckOptions) for prvCheckOptions
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_state_handling.o(i.prvTCPHandleState) for prvTCPHandleState
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_transmission.o(i.prvTCPSendRepeated) for prvTCPSendRepeated
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_tcp_ip.o(i.xProcessReceivedTCPPacket) refers to freertos_tcp_ip.o(i.prvTCPNextTimeout) for prvTCPNextTimeout
    freertos_tcp_ip.o(i.xTCPCheckNewClient) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_ip.o(i.xTCPCheckNewClient) refers to freertos_sockets.o(.bss) for xBoundTCPSocketsList
    freertos_tcp_ip.o(i.xTCPSocketCheck) refers to freertos_tcp_transmission.o(i.prvTCPAddTxData) for prvTCPAddTxData
    freertos_tcp_ip.o(i.xTCPSocketCheck) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_ip.o(i.xTCPSocketCheck) refers to freertos_tcp_transmission.o(i.prvTCPReturnPacket) for prvTCPReturnPacket
    freertos_tcp_ip.o(i.xTCPSocketCheck) refers to freertos_tcp_ip.o(i.prvTCPNextTimeout) for prvTCPNextTimeout
    freertos_tcp_ip.o(i.xTCPSocketCheck) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_tcp_ip.o(i.xTCPSocketCheck) refers to freertos_tcp_transmission.o(i.prvTCPSendPacket) for prvTCPSendPacket
    freertos_tcp_ip.o(i.xTCPSocketCheck) refers to freertos_tcp_state_handling.o(i.prvTCPStatusAgeCheck) for prvTCPStatusAgeCheck
    freertos_tcp_ip.o(i.xTCPSocketCheck) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_reception.o(i.prvCheckOptions) refers to freertos_tcp_reception.o(i.prvSingleStepTCPHeaderOptions) for prvSingleStepTCPHeaderOptions
    freertos_tcp_reception.o(i.prvCheckRxData) refers to freertos_ip_utils.o(i.FreeRTOS_min_int32) for FreeRTOS_min_int32
    freertos_tcp_reception.o(i.prvReadSackOption) refers to freertos_ip_utils.o(i.ulChar2u32) for ulChar2u32
    freertos_tcp_reception.o(i.prvReadSackOption) refers to freertos_tcp_win.o(i.ulTCPWindowTxSack) for ulTCPWindowTxSack
    freertos_tcp_reception.o(i.prvReadSackOption) refers to freertos_stream_buffer.o(i.uxStreamBufferGet) for uxStreamBufferGet
    freertos_tcp_reception.o(i.prvSingleStepTCPHeaderOptions) refers to freertos_ip_utils.o(i.usChar2u16) for usChar2u16
    freertos_tcp_reception.o(i.prvSingleStepTCPHeaderOptions) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_reception.o(i.prvSingleStepTCPHeaderOptions) refers to freertos_tcp_reception.o(i.prvReadSackOption) for prvReadSackOption
    freertos_tcp_reception.o(i.prvStoreRxData) refers to freertos_stream_buffer.o(i.uxStreamBufferGetSpace) for uxStreamBufferGetSpace
    freertos_tcp_reception.o(i.prvStoreRxData) refers to freertos_tcp_win.o(i.lTCPWindowRxCheck) for lTCPWindowRxCheck
    freertos_tcp_reception.o(i.prvStoreRxData) refers to freertos_sockets.o(i.lTCPAddRxdata) for lTCPAddRxdata
    freertos_tcp_reception.o(i.prvStoreRxData) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_reception.o(i.prvStoreRxData) refers to freertos_tcp_transmission.o(i.prvTCPSendReset) for prvTCPSendReset
    freertos_tcp_state_handling.o(i.FreeRTOS_GetTCPStateName) refers to freertos_tcp_state_handling.o(.constdata) for pcStateNames
    freertos_tcp_state_handling.o(i.prvHandleEstablished) refers to freertos_tcp_win.o(i.ulTCPWindowTxAck) for ulTCPWindowTxAck
    freertos_tcp_state_handling.o(i.prvHandleEstablished) refers to freertos_stream_buffer.o(i.uxStreamBufferGet) for uxStreamBufferGet
    freertos_tcp_state_handling.o(i.prvHandleEstablished) refers to freertos_tcp_transmission.o(i.prvTCPAddTxData) for prvTCPAddTxData
    freertos_tcp_state_handling.o(i.prvHandleEstablished) refers to freertos_tcp_win.o(i.xTCPWindowRxEmpty) for xTCPWindowRxEmpty
    freertos_tcp_state_handling.o(i.prvHandleEstablished) refers to freertos_tcp_win.o(i.xTCPWindowTxDone) for xTCPWindowTxDone
    freertos_tcp_state_handling.o(i.prvHandleEstablished) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_state_handling.o(i.prvHandleEstablished) refers to freertos_tcp_state_handling.o(i.prvTCPHandleFin) for prvTCPHandleFin
    freertos_tcp_state_handling.o(i.prvHandleEstablished) refers to freertos_tcp_transmission.o(i.prvTCPPrepareSend) for prvTCPPrepareSend
    freertos_tcp_state_handling.o(i.prvHandleEstablished) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to networkinterface.o(i.ulApplicationGetNextSequenceNumber) for ulApplicationGetNextSequenceNumber
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to freertos_tcp_transmission.o(i.prvTCPSendReset) for prvTCPSendReset
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to freertos_sockets.o(i.FreeRTOS_socket) for FreeRTOS_socket
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to freertos_tcp_state_handling.o(i.prvTCPSocketCopy) for prvTCPSocketCopy
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to freertos_tcp_utils.o(i.prvSocketSetMSS) for prvSocketSetMSS
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to freertos_tcp_transmission.o(i.prvTCPCreateWindow) for prvTCPCreateWindow
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_tcp_state_handling.o(i.prvHandleListen) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_tcp_state_handling.o(i.prvHandleSynReceived) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_state_handling.o(i.prvHandleSynReceived) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_tcp_state_handling.o(i.prvHandleSynReceived) refers to freertos_tcp_win.o(i.vTCPWindowInit) for vTCPWindowInit
    freertos_tcp_state_handling.o(i.prvTCPHandleFin) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_tcp_state_handling.o(i.prvTCPHandleFin) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_state_handling.o(i.prvTCPHandleFin) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to freertos_tcp_reception.o(i.prvCheckRxData) for prvCheckRxData
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to freertos_tcp_reception.o(i.prvStoreRxData) for prvStoreRxData
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to freertos_tcp_transmission.o(i.prvSetOptions) for prvSetOptions
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to freertos_tcp_transmission.o(i.prvSetSynAckOptions) for prvSetSynAckOptions
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to freertos_tcp_state_handling.o(i.prvHandleSynReceived) for prvHandleSynReceived
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to freertos_tcp_state_handling.o(i.prvHandleEstablished) for prvHandleEstablished
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to freertos_tcp_state_handling.o(i.prvTCPHandleFin) for prvTCPHandleFin
    freertos_tcp_state_handling.o(i.prvTCPHandleState) refers to freertos_tcp_transmission.o(i.prvSendData) for prvSendData
    freertos_tcp_state_handling.o(i.prvTCPSocketCopy) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_state_handling.o(i.prvTCPSocketCopy) refers to freertos_sockets.o(i.vSocketBind) for vSocketBind
    freertos_tcp_state_handling.o(i.prvTCPSocketCopy) refers to freertos_sockets.o(i.vSocketClose) for vSocketClose
    freertos_tcp_state_handling.o(i.prvTCPSocketCopy) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_tcp_state_handling.o(i.prvTCPStatusAgeCheck) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    freertos_tcp_state_handling.o(i.prvTCPStatusAgeCheck) refers to freertos_tcp_state_handling.o(i.FreeRTOS_GetTCPStateName) for FreeRTOS_GetTCPStateName
    freertos_tcp_state_handling.o(i.prvTCPStatusAgeCheck) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_state_handling.o(i.prvTCPStatusAgeCheck) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_tcp_state_handling.o(.constdata) refers to freertos_tcp_state_handling.o(.conststring) for .conststring
    freertos_tcp_transmission.o(i.prvSendData) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_tcp_transmission.o(i.prvSendData) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_transmission.o(i.prvSendData) refers to freertos_tcp_transmission.o(i.prvTCPReturnPacket) for prvTCPReturnPacket
    freertos_tcp_transmission.o(i.prvSendData) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_transmission.o(i.prvSetOptions) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_transmission.o(i.prvSetOptions) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_tcp_transmission.o(i.prvSetOptions) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_transmission.o(i.prvSetSynAckOptions) refers to freertos_tcp_transmission.o(i.prvWinScaleFactor) for prvWinScaleFactor
    freertos_tcp_transmission.o(i.prvTCPAddTxData) refers to freertos_stream_buffer.o(i.uxStreamBufferMidSpace) for uxStreamBufferMidSpace
    freertos_tcp_transmission.o(i.prvTCPAddTxData) refers to freertos_tcp_win.o(i.lTCPWindowTxAdd) for lTCPWindowTxAdd
    freertos_tcp_transmission.o(i.prvTCPAddTxData) refers to freertos_stream_buffer.o(i.vStreamBufferMoveMid) for vStreamBufferMoveMid
    freertos_tcp_transmission.o(i.prvTCPBufferResize) refers to bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) for pxGetNetworkBufferWithDescriptor
    freertos_tcp_transmission.o(i.prvTCPBufferResize) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_tcp_transmission.o(i.prvTCPBufferResize) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_tcp_transmission.o(i.prvTCPBufferResize) refers to bufferallocation_2.o(.constdata) for xBufferAllocFixedSize
    freertos_tcp_transmission.o(i.prvTCPCreateWindow) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_transmission.o(i.prvTCPCreateWindow) refers to freertos_tcp_win.o(i.vTCPWindowCreate) for vTCPWindowCreate
    freertos_tcp_transmission.o(i.prvTCPCreateWindow) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_transmission.o(i.prvTCPMakeSurePrepared) refers to freertos_tcp_transmission.o(i.prvTCPPrepareConnect) for prvTCPPrepareConnect
    freertos_tcp_transmission.o(i.prvTCPPrepareConnect) refers to freertos_arp.o(i.eARPGetCacheEntry) for eARPGetCacheEntry
    freertos_tcp_transmission.o(i.prvTCPPrepareConnect) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_transmission.o(i.prvTCPPrepareConnect) refers to freertos_arp.o(i.FreeRTOS_OutputARPRequest) for FreeRTOS_OutputARPRequest
    freertos_tcp_transmission.o(i.prvTCPPrepareConnect) refers to networkinterface.o(i.ulApplicationGetNextSequenceNumber) for ulApplicationGetNextSequenceNumber
    freertos_tcp_transmission.o(i.prvTCPPrepareConnect) refers to rt_memclr.o(.text) for __aeabi_memclr
    freertos_tcp_transmission.o(i.prvTCPPrepareConnect) refers to freertos_tcp_utils.o(i.prvSocketSetMSS) for prvSocketSetMSS
    freertos_tcp_transmission.o(i.prvTCPPrepareConnect) refers to freertos_tcp_transmission.o(i.prvTCPCreateWindow) for prvTCPCreateWindow
    freertos_tcp_transmission.o(i.prvTCPPrepareConnect) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_tcp_transmission.o(i.prvTCPPrepareSend) refers to freertos_tcp_win.o(i.ulTCPWindowTxGet) for ulTCPWindowTxGet
    freertos_tcp_transmission.o(i.prvTCPPrepareSend) refers to freertos_tcp_transmission.o(i.prvTCPBufferResize) for prvTCPBufferResize
    freertos_tcp_transmission.o(i.prvTCPPrepareSend) refers to freertos_stream_buffer.o(i.uxStreamBufferDistance) for uxStreamBufferDistance
    freertos_tcp_transmission.o(i.prvTCPPrepareSend) refers to freertos_stream_buffer.o(i.uxStreamBufferGet) for uxStreamBufferGet
    freertos_tcp_transmission.o(i.prvTCPPrepareSend) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_transmission.o(i.prvTCPPrepareSend) refers to freertos_tcp_win.o(i.xTCPWindowTxDone) for xTCPWindowTxDone
    freertos_tcp_transmission.o(i.prvTCPPrepareSend) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_tcp_transmission.o(i.prvTCPPrepareSend) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    freertos_tcp_transmission.o(i.prvTCPPrepareSend) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_transmission.o(i.prvTCPReturnPacket) refers to freertos_stream_buffer.o(i.uxStreamBufferFrontSpace) for uxStreamBufferFrontSpace
    freertos_tcp_transmission.o(i.prvTCPReturnPacket) refers to freertos_ip_utils.o(i.FreeRTOS_min_uint32) for FreeRTOS_min_uint32
    freertos_tcp_transmission.o(i.prvTCPReturnPacket) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_transmission.o(i.prvTCPReturnPacket) refers to freertos_ip_utils.o(i.usGenerateChecksum) for usGenerateChecksum
    freertos_tcp_transmission.o(i.prvTCPReturnPacket) refers to freertos_ip_utils.o(i.usGenerateProtocolChecksum) for usGenerateProtocolChecksum
    freertos_tcp_transmission.o(i.prvTCPReturnPacket) refers to freertos_arp.o(i.eARPGetCacheEntry) for eARPGetCacheEntry
    freertos_tcp_transmission.o(i.prvTCPReturnPacket) refers to networkinterface.o(i.xNetworkInterfaceOutput) for xNetworkInterfaceOutput
    freertos_tcp_transmission.o(i.prvTCPReturnPacket) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_tcp_transmission.o(i.prvTCPReturnPacket) refers to freertos_ip.o(.data) for usPacketIdentifier
    freertos_tcp_transmission.o(i.prvTCPSendChallengeAck) refers to freertos_tcp_transmission.o(i.prvTCPSendSpecialPacketHelper) for prvTCPSendSpecialPacketHelper
    freertos_tcp_transmission.o(i.prvTCPSendPacket) refers to freertos_tcp_transmission.o(i.prvTCPSendRepeated) for prvTCPSendRepeated
    freertos_tcp_transmission.o(i.prvTCPSendPacket) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_tcp_transmission.o(i.prvTCPSendPacket) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_transmission.o(i.prvTCPSendPacket) refers to freertos_tcp_ip.o(i.vTCPStateChange) for vTCPStateChange
    freertos_tcp_transmission.o(i.prvTCPSendPacket) refers to freertos_tcp_transmission.o(i.prvTCPMakeSurePrepared) for prvTCPMakeSurePrepared
    freertos_tcp_transmission.o(i.prvTCPSendPacket) refers to freertos_tcp_transmission.o(i.prvSetSynAckOptions) for prvSetSynAckOptions
    freertos_tcp_transmission.o(i.prvTCPSendPacket) refers to freertos_tcp_transmission.o(i.prvTCPReturnPacket) for prvTCPReturnPacket
    freertos_tcp_transmission.o(i.prvTCPSendRepeated) refers to freertos_tcp_transmission.o(i.prvTCPPrepareSend) for prvTCPPrepareSend
    freertos_tcp_transmission.o(i.prvTCPSendRepeated) refers to freertos_tcp_transmission.o(i.prvTCPReturnPacket) for prvTCPReturnPacket
    freertos_tcp_transmission.o(i.prvTCPSendReset) refers to freertos_tcp_transmission.o(i.prvTCPSendSpecialPacketHelper) for prvTCPSendSpecialPacketHelper
    freertos_tcp_transmission.o(i.prvTCPSendSpecialPacketHelper) refers to freertos_tcp_transmission.o(i.prvTCPReturnPacket) for prvTCPReturnPacket
    freertos_tcp_transmission.o(i.prvWinScaleFactor) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_utils.o(i.prvSocketSetMSS) refers to freertos_ip_utils.o(i.FreeRTOS_min_uint32) for FreeRTOS_min_uint32
    freertos_tcp_utils.o(i.prvSocketSetMSS) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_utils.o(i.prvSocketSetMSS) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_tcp_utils.o(i.prvSocketSetMSS) refers to freertos_ip.o(.bss) for xNetworkAddressing
    freertos_tcp_utils.o(i.prvTCPFlagMeaning) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    freertos_tcp_utils.o(i.prvTCPFlagMeaning) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    freertos_tcp_utils.o(i.prvTCPFlagMeaning) refers to _printf_str.o(.text) for _printf_str
    freertos_tcp_utils.o(i.prvTCPFlagMeaning) refers to noretval__2snprintf.o(.text) for __2snprintf
    freertos_tcp_utils.o(i.prvTCPFlagMeaning) refers to freertos_tcp_utils.o(.bss) for retString
    freertos_tcp_win.o(i.lTCPWindowRxCheck) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.lTCPWindowRxCheck) refers to freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX) for prvTCPWindowRx_ExpectedRX
    freertos_tcp_win.o(i.lTCPWindowRxCheck) refers to freertos_tcp_win.o(i.prvTCPWindowRx_UnexpectedRX) for prvTCPWindowRx_UnexpectedRX
    freertos_tcp_win.o(i.lTCPWindowRxCheck) refers to freertos_tcp_win.o(.conststring) for .conststring
    freertos_tcp_win.o(i.lTCPWindowTxAdd) refers to freertos_tcp_win.o(i.prvTCPWindowTxAdd_FrontSegment) for prvTCPWindowTxAdd_FrontSegment
    freertos_tcp_win.o(i.lTCPWindowTxAdd) refers to freertos_tcp_win.o(i.lTCPIncrementTxPosition) for lTCPIncrementTxPosition
    freertos_tcp_win.o(i.lTCPWindowTxAdd) refers to freertos_tcp_win.o(i.xTCPWindowNew) for xTCPWindowNew
    freertos_tcp_win.o(i.lTCPWindowTxAdd) refers to freertos_ip_utils.o(i.FreeRTOS_min_int32) for FreeRTOS_min_int32
    freertos_tcp_win.o(i.lTCPWindowTxAdd) refers to freertos_tcp_win.o(i.vListInsertFifo) for vListInsertFifo
    freertos_tcp_win.o(i.lTCPWindowTxAdd) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.prvCreateSectors) refers to list.o(i.vListInitialise) for vListInitialise
    freertos_tcp_win.o(i.prvCreateSectors) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    freertos_tcp_win.o(i.prvCreateSectors) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.prvCreateSectors) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    freertos_tcp_win.o(i.prvCreateSectors) refers to freertos_tcp_win.o(i.vListInsertFifo) for vListInsertFifo
    freertos_tcp_win.o(i.prvCreateSectors) refers to freertos_tcp_win.o(.bss) for xSegmentList
    freertos_tcp_win.o(i.prvCreateSectors) refers to freertos_tcp_win.o(.data) for xTCPSegments
    freertos_tcp_win.o(i.prvTCPWindowFastRetransmit) refers to freertos_tcp_win.o(i.xSequenceLessThan) for xSequenceLessThan
    freertos_tcp_win.o(i.prvTCPWindowFastRetransmit) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.prvTCPWindowFastRetransmit) refers to list.o(i.uxListRemove) for uxListRemove
    freertos_tcp_win.o(i.prvTCPWindowFastRetransmit) refers to freertos_tcp_win.o(i.vListInsertFifo) for vListInsertFifo
    freertos_tcp_win.o(i.prvTCPWindowFastRetransmit) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX) refers to freertos_tcp_win.o(i.xTCPWindowRxConfirm) for xTCPWindowRxConfirm
    freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX) refers to freertos_tcp_win.o(i.vTCPWindowFree) for vTCPWindowFree
    freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX) refers to freertos_tcp_win.o(i.xTCPWindowRxFind) for xTCPWindowRxFind
    freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX) refers to freertos_tcp_win.o(.conststring) for .conststring
    freertos_tcp_win.o(i.prvTCPWindowRx_UnexpectedRX) refers to freertos_tcp_win.o(i.xTCPWindowRxFind) for xTCPWindowRxFind
    freertos_tcp_win.o(i.prvTCPWindowRx_UnexpectedRX) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.prvTCPWindowRx_UnexpectedRX) refers to freertos_tcp_win.o(i.xTCPWindowNew) for xTCPWindowNew
    freertos_tcp_win.o(i.prvTCPWindowRx_UnexpectedRX) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_win.o(i.prvTCPWindowTxAdd_FrontSegment) refers to freertos_ip_utils.o(i.FreeRTOS_min_int32) for FreeRTOS_min_int32
    freertos_tcp_win.o(i.prvTCPWindowTxAdd_FrontSegment) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.prvTCPWindowTxAdd_FrontSegment) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_win.o(i.prvTCPWindowTxAdd_FrontSegment) refers to freertos_tcp_win.o(.conststring) for .conststring
    freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) refers to freertos_tcp_win.o(i.xSequenceGreaterThan) for xSequenceGreaterThan
    freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) refers to freertos_tcp_win.o(i.prvTCPWindowTxCheckAck_CalcSRTT) for prvTCPWindowTxCheckAck_CalcSRTT
    freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) refers to freertos_tcp_win.o(i.vTCPWindowFree) for vTCPWindowFree
    freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) refers to list.o(i.uxListRemove) for uxListRemove
    freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) refers to freertos_tcp_win.o(i.xSequenceLessThan) for xSequenceLessThan
    freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) refers to freertos_tcp_win.o(.conststring) for .conststring
    freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_win.o(i.prvTCPWindowTxCheckAck_CalcSRTT) refers to freertos_tcp_win.o(i.ulTimerGetAge) for ulTimerGetAge
    freertos_tcp_win.o(i.prvTCPWindowTxHasSpace) refers to freertos_tcp_win.o(i.xTCPWindowPeekHead) for xTCPWindowPeekHead
    freertos_tcp_win.o(i.prvTCPWindowTxHasSpace) refers to freertos_ip_utils.o(i.FreeRTOS_min_uint32) for FreeRTOS_min_uint32
    freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue) refers to freertos_tcp_win.o(i.xTCPWindowPeekHead) for xTCPWindowPeekHead
    freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue) refers to freertos_tcp_win.o(i.prvTCPWindowTxHasSpace) for prvTCPWindowTxHasSpace
    freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue) refers to freertos_tcp_win.o(i.xTCPWindowGetHead) for xTCPWindowGetHead
    freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue) refers to freertos_tcp_win.o(.conststring) for .conststring
    freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue) refers to freertos_tcp_win.o(i.xTCPWindowPeekHead) for xTCPWindowPeekHead
    freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue) refers to freertos_tcp_win.o(i.ulTimerGetAge) for ulTimerGetAge
    freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue) refers to freertos_tcp_win.o(i.xTCPWindowGetHead) for xTCPWindowGetHead
    freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue) refers to freertos_tcp_win.o(.conststring) for .conststring
    freertos_tcp_win.o(i.ulTCPWindowTxAck) refers to freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) for prvTCPWindowTxCheckAck
    freertos_tcp_win.o(i.ulTCPWindowTxGet) refers to freertos_tcp_win.o(i.xTCPWindowGetHead) for xTCPWindowGetHead
    freertos_tcp_win.o(i.ulTCPWindowTxGet) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.ulTCPWindowTxGet) refers to freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue) for pxTCPWindowTx_GetWaitQueue
    freertos_tcp_win.o(i.ulTCPWindowTxGet) refers to freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue) for pxTCPWindowTx_GetTXQueue
    freertos_tcp_win.o(i.ulTCPWindowTxGet) refers to freertos_tcp_win.o(i.vListInsertFifo) for vListInsertFifo
    freertos_tcp_win.o(i.ulTCPWindowTxGet) refers to freertos_tcp_win.o(i.vTCPTimerSet) for vTCPTimerSet
    freertos_tcp_win.o(i.ulTCPWindowTxGet) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_win.o(i.ulTCPWindowTxGet) refers to freertos_tcp_win.o(.conststring) for .conststring
    freertos_tcp_win.o(i.ulTCPWindowTxSack) refers to freertos_tcp_win.o(i.prvTCPWindowTxCheckAck) for prvTCPWindowTxCheckAck
    freertos_tcp_win.o(i.ulTCPWindowTxSack) refers to freertos_tcp_win.o(i.prvTCPWindowFastRetransmit) for prvTCPWindowFastRetransmit
    freertos_tcp_win.o(i.ulTCPWindowTxSack) refers to freertos_tcp_win.o(i.xSequenceGreaterThan) for xSequenceGreaterThan
    freertos_tcp_win.o(i.ulTCPWindowTxSack) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.ulTCPWindowTxSack) refers to freertos_tcp_win.o(.data) for xTCPWindowLoggingLevel
    freertos_tcp_win.o(i.ulTimerGetAge) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    freertos_tcp_win.o(i.vListInsertFifo) refers to freertos_tcp_win.o(i.vListInsertGeneric) for vListInsertGeneric
    freertos_tcp_win.o(i.vTCPSegmentCleanup) refers to heap_4.o(i.vPortFree) for vPortFree
    freertos_tcp_win.o(i.vTCPSegmentCleanup) refers to freertos_tcp_win.o(.data) for xTCPSegments
    freertos_tcp_win.o(i.vTCPTimerSet) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    freertos_tcp_win.o(i.vTCPWindowCreate) refers to freertos_tcp_win.o(i.prvCreateSectors) for prvCreateSectors
    freertos_tcp_win.o(i.vTCPWindowCreate) refers to list.o(i.vListInitialise) for vListInitialise
    freertos_tcp_win.o(i.vTCPWindowCreate) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.vTCPWindowCreate) refers to freertos_tcp_win.o(i.vTCPWindowInit) for vTCPWindowInit
    freertos_tcp_win.o(i.vTCPWindowCreate) refers to freertos_tcp_win.o(.data) for xTCPSegments
    freertos_tcp_win.o(i.vTCPWindowDestroy) refers to freertos_tcp_win.o(i.vTCPWindowFree) for vTCPWindowFree
    freertos_tcp_win.o(i.vTCPWindowFree) refers to list.o(i.uxListRemove) for uxListRemove
    freertos_tcp_win.o(i.vTCPWindowFree) refers to freertos_tcp_win.o(i.vListInsertFifo) for vListInsertFifo
    freertos_tcp_win.o(i.vTCPWindowFree) refers to freertos_tcp_win.o(.bss) for xSegmentList
    freertos_tcp_win.o(i.xTCPWindowGetHead) refers to list.o(i.uxListRemove) for uxListRemove
    freertos_tcp_win.o(i.xTCPWindowNew) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.xTCPWindowNew) refers to list.o(i.uxListRemove) for uxListRemove
    freertos_tcp_win.o(i.xTCPWindowNew) refers to freertos_tcp_win.o(i.vListInsertFifo) for vListInsertFifo
    freertos_tcp_win.o(i.xTCPWindowNew) refers to freertos_tcp_win.o(i.vTCPTimerSet) for vTCPTimerSet
    freertos_tcp_win.o(i.xTCPWindowNew) refers to freertos_tcp_win.o(.bss) for xSegmentList
    freertos_tcp_win.o(i.xTCPWindowNew) refers to freertos_tcp_win.o(.data) for xLowestLength
    freertos_tcp_win.o(i.xTCPWindowRxConfirm) refers to freertos_tcp_win.o(i.xSequenceGreaterThanOrEqual) for xSequenceGreaterThanOrEqual
    freertos_tcp_win.o(i.xTCPWindowRxConfirm) refers to freertos_tcp_win.o(i.xSequenceLessThan) for xSequenceLessThan
    freertos_tcp_win.o(i.xTCPWindowRxConfirm) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.xTCPWindowRxEmpty) refers to freertos_tcp_win.o(i.xSequenceGreaterThanOrEqual) for xSequenceGreaterThanOrEqual
    freertos_tcp_win.o(i.xTCPWindowRxEmpty) refers to networkinterface.o(i.vLoggingPrintf) for vLoggingPrintf
    freertos_tcp_win.o(i.xTCPWindowTxHasData) refers to freertos_tcp_win.o(i.xTCPWindowPeekHead) for xTCPWindowPeekHead
    freertos_tcp_win.o(i.xTCPWindowTxHasData) refers to freertos_tcp_win.o(i.ulTimerGetAge) for ulTimerGetAge
    freertos_tcp_win.o(i.xTCPWindowTxHasData) refers to freertos_tcp_win.o(i.prvTCPWindowTxHasSpace) for prvTCPWindowTxHasSpace
    freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) refers to freertos_arp.o(i.eARPGetCacheEntry) for eARPGetCacheEntry
    freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) refers to freertos_ip_utils.o(i.usGenerateChecksum) for usGenerateChecksum
    freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) refers to freertos_ip_utils.o(i.usGenerateProtocolChecksum) for usGenerateProtocolChecksum
    freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) refers to freertos_arp.o(i.vARPRefreshCacheEntry) for vARPRefreshCacheEntry
    freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) refers to freertos_arp.o(i.vARPGenerateRequestPacket) for vARPGenerateRequestPacket
    freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) refers to networkinterface.o(i.xNetworkInterfaceOutput) for xNetworkInterfaceOutput
    freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    freertos_udp_ip.o(i.vProcessGeneratedUDPPacket) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to freertos_sockets.o(i.pxUDPSocketLookup) for pxUDPSocketLookup
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to freertos_arp.o(i.xCheckRequiresARPResolution) for xCheckRequiresARPResolution
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to freertos_arp.o(i.vARPRefreshCacheEntry) for vARPRefreshCacheEntry
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to freertos_dns.o(i.ulDNSHandlePacket) for ulDNSHandlePacket
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to freertos_dns.o(i.ulNBNSHandlePacket) for ulNBNSHandlePacket
    freertos_udp_ip.o(i.xProcessReceivedUDPPacket) refers to freertos_udp_ip.o(.data) for xDefaultPartUDPPacketHeader
    bufferallocation_2.o(i.pucGetNetworkBuffer) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) refers to list.o(i.uxListRemove) for uxListRemove
    bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) refers to bufferallocation_2.o(.data) for xNetworkBufferSemaphore
    bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) refers to bufferallocation_2.o(.bss) for xFreeBuffersList
    bufferallocation_2.o(i.pxResizeNetworkBufferWithDescriptor) refers to bufferallocation_2.o(i.pucGetNetworkBuffer) for pucGetNetworkBuffer
    bufferallocation_2.o(i.pxResizeNetworkBufferWithDescriptor) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    bufferallocation_2.o(i.pxResizeNetworkBufferWithDescriptor) refers to bufferallocation_2.o(i.vReleaseNetworkBuffer) for vReleaseNetworkBuffer
    bufferallocation_2.o(i.uxGetMinimumFreeNetworkBuffers) refers to bufferallocation_2.o(.data) for uxMinimumFreeNetworkBuffers
    bufferallocation_2.o(i.uxGetNumberOfFreeNetworkBuffers) refers to bufferallocation_2.o(.bss) for xFreeBuffersList
    bufferallocation_2.o(i.vReleaseNetworkBuffer) refers to heap_4.o(i.vPortFree) for vPortFree
    bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) refers to bufferallocation_2.o(i.vReleaseNetworkBuffer) for vReleaseNetworkBuffer
    bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) refers to bufferallocation_2.o(.bss) for xFreeBuffersList
    bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) refers to bufferallocation_2.o(.data) for xNetworkBufferSemaphore
    bufferallocation_2.o(i.xNetworkBuffersInitialise) refers to queue.o(i.xQueueCreateCountingSemaphore) for xQueueCreateCountingSemaphore
    bufferallocation_2.o(i.xNetworkBuffersInitialise) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    bufferallocation_2.o(i.xNetworkBuffersInitialise) refers to list.o(i.vListInitialise) for vListInitialise
    bufferallocation_2.o(i.xNetworkBuffersInitialise) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    bufferallocation_2.o(i.xNetworkBuffersInitialise) refers to list.o(i.vListInsert) for vListInsert
    bufferallocation_2.o(i.xNetworkBuffersInitialise) refers to bufferallocation_2.o(.data) for xNetworkBufferSemaphore
    bufferallocation_2.o(i.xNetworkBuffersInitialise) refers to bufferallocation_2.o(.bss) for xFreeBuffersList
    networkinterface.o(i.ENET_IRQHandler) refers to gd32f4xx_enet.o(i.enet_interrupt_flag_get) for enet_interrupt_flag_get
    networkinterface.o(i.ENET_IRQHandler) refers to tasks.o(i.vTaskGenericNotifyGiveFromISR) for vTaskGenericNotifyGiveFromISR
    networkinterface.o(i.ENET_IRQHandler) refers to gd32f4xx_enet.o(i.enet_interrupt_flag_clear) for enet_interrupt_flag_clear
    networkinterface.o(i.ENET_IRQHandler) refers to networkinterface.o(.data) for xEMACTaskHandle
    networkinterface.o(i.eth_rece_data_task) refers to tasks.o(i.ulTaskGenericNotifyTake) for ulTaskGenericNotifyTake
    networkinterface.o(i.eth_rece_data_task) refers to gd32f4xx_enet.o(i.enet_rxframe_size_get) for enet_rxframe_size_get
    networkinterface.o(i.eth_rece_data_task) refers to bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor) for pxGetNetworkBufferWithDescriptor
    networkinterface.o(i.eth_rece_data_task) refers to gd32f4xx_enet.o(i.enet_frame_receive) for enet_frame_receive
    networkinterface.o(i.eth_rece_data_task) refers to freertos_ip.o(i.eConsiderFrameForProcessing) for eConsiderFrameForProcessing
    networkinterface.o(i.eth_rece_data_task) refers to freertos_ip.o(i.xSendEventStructToIPTask) for xSendEventStructToIPTask
    networkinterface.o(i.eth_rece_data_task) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    networkinterface.o(i.eth_rece_data_task) refers to noretval__2printf.o(.text) for __2printf
    networkinterface.o(i.ethernet_task_creation) refers to noretval__2printf.o(.text) for __2printf
    networkinterface.o(i.ethernet_task_creation) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    networkinterface.o(i.ethernet_task_creation) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    networkinterface.o(i.ethernet_task_creation) refers to networkinterface.o(.data) for xPingReplyQueue
    networkinterface.o(i.ethernet_task_creation) refers to networkinterface.o(i.eth_rece_data_task) for eth_rece_data_task
    networkinterface.o(i.ulApplicationGetNextSequenceNumber) refers to networkinterface.o(i.uxRand) for uxRand
    networkinterface.o(i.uxRand) refers to trng.o(i.trng_random_range_get) for trng_random_range_get
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to _printf_dec.o(.text) for _printf_int_dec
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to _printf_str.o(.text) for _printf_str
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to noretval__2printf.o(.text) for __2printf
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to networkinterface.o(i.ethernet_task_creation) for ethernet_task_creation
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to freertos_ip.o(i.FreeRTOS_GetAddressConfiguration) for FreeRTOS_GetAddressConfiguration
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to freertos_sockets.o(i.FreeRTOS_inet_ntoa) for FreeRTOS_inet_ntoa
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to networkinterface.o(.data) for xTasksAlreadyCreated
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to main.o(i.tcp_server_task) for tcp_server_task
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to main.o(i.http_server_task) for http_server_task
    networkinterface.o(i.vApplicationIPNetworkEventHook) refers to main.o(i.clock_test_task) for clock_test_task
    networkinterface.o(i.xApplicationGetRandomNumber) refers to networkinterface.o(i.uxRand) for uxRand
    networkinterface.o(i.xNetworkInterfaceInitialise) refers to enet.o(i.InitialiseNetwork) for InitialiseNetwork
    networkinterface.o(i.xNetworkInterfaceInitialise) refers to noretval__2printf.o(.text) for __2printf
    networkinterface.o(i.xNetworkInterfaceOutput) refers to gd32f4xx_enet.o(i.enet_frame_transmit) for enet_frame_transmit
    networkinterface.o(i.xNetworkInterfaceOutput) refers to bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor) for vReleaseNetworkBufferAndDescriptor
    main.o(i.clock_test_task) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.clock_test_task) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(i.clock_test_task) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.clock_test_task) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.clock_test_task) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    main.o(i.clock_test_task) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    main.o(i.enet_inti_task) refers to trng.o(i.trng_init) for trng_init
    main.o(i.enet_inti_task) refers to freertos_ip.o(i.FreeRTOS_IPInit) for FreeRTOS_IPInit
    main.o(i.enet_inti_task) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    main.o(i.enet_inti_task) refers to main.o(.data) for ucMACAddress
    main.o(i.http_server_task) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.http_server_task) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.http_server_task) refers to _printf_str.o(.text) for _printf_str
    main.o(i.http_server_task) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.http_server_task) refers to freertos_sockets.o(i.FreeRTOS_socket) for FreeRTOS_socket
    main.o(i.http_server_task) refers to freertos_sockets.o(i.FreeRTOS_bind) for FreeRTOS_bind
    main.o(i.http_server_task) refers to freertos_sockets.o(i.FreeRTOS_listen) for FreeRTOS_listen
    main.o(i.http_server_task) refers to freertos_sockets.o(i.FreeRTOS_accept) for FreeRTOS_accept
    main.o(i.http_server_task) refers to freertos_sockets.o(i.FreeRTOS_recv) for FreeRTOS_recv
    main.o(i.http_server_task) refers to strlen.o(.text) for strlen
    main.o(i.http_server_task) refers to freertos_sockets.o(i.FreeRTOS_send) for FreeRTOS_send
    main.o(i.http_server_task) refers to freertos_sockets.o(i.FreeRTOS_closesocket) for FreeRTOS_closesocket
    main.o(i.http_server_task) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    main.o(i.http_server_task) refers to main.o(.conststring) for .conststring
    main.o(i.main) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    main.o(i.main) refers to uart0.o(i.uart0_init) for uart0_init
    main.o(i.main) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    main.o(i.main) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    main.o(i.main) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.main) refers to main.o(i.enet_inti_task) for enet_inti_task
    main.o(i.main) refers to main.o(i.start_task) for start_task
    main.o(i.start_task) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(i.start_task) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.start_task) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    main.o(i.start_task) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    main.o(i.start_task) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.start_task) refers to _printf_str.o(.text) for _printf_str
    main.o(i.start_task) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.start_task) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    main.o(i.start_task) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    main.o(i.tcp_server_task) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.tcp_server_task) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(i.tcp_server_task) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(i.tcp_server_task) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.tcp_server_task) refers to _printf_str.o(.text) for _printf_str
    main.o(i.tcp_server_task) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.tcp_server_task) refers to freertos_sockets.o(i.FreeRTOS_socket) for FreeRTOS_socket
    main.o(i.tcp_server_task) refers to freertos_sockets.o(i.FreeRTOS_bind) for FreeRTOS_bind
    main.o(i.tcp_server_task) refers to freertos_sockets.o(i.FreeRTOS_listen) for FreeRTOS_listen
    main.o(i.tcp_server_task) refers to freertos_sockets.o(i.FreeRTOS_accept) for FreeRTOS_accept
    main.o(i.tcp_server_task) refers to strlen.o(.text) for strlen
    main.o(i.tcp_server_task) refers to freertos_sockets.o(i.FreeRTOS_send) for FreeRTOS_send
    main.o(i.tcp_server_task) refers to freertos_sockets.o(i.FreeRTOS_recv) for FreeRTOS_recv
    main.o(i.tcp_server_task) refers to freertos_sockets.o(i.FreeRTOS_closesocket) for FreeRTOS_closesocket
    main.o(i.tcp_server_task) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    uart0.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    uart0.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    uart0.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    uart0.o(i._ttywrch) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    uart0.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    uart0.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    uart0.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    uart0.o(i.uart0_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    uart0.o(i.uart0_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    uart0.o(i.uart0_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    uart0.o(i.uart0_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    uart0.o(i.uart0_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    uart0.o(i.uart0_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    uart0.o(i.uart0_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    uart0.o(i.uart0_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    uart0.o(i.uart0_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    uart0.o(i.uart0_send_data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    uart0.o(i.uart0_send_data) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    uart0.o(i.uart0_send_data) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    uart0.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    enet.o(i.InitialiseNetwork) refers to _printf_pad.o(.text) for _printf_pre_padding
    enet.o(i.InitialiseNetwork) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    enet.o(i.InitialiseNetwork) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    enet.o(i.InitialiseNetwork) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    enet.o(i.InitialiseNetwork) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    enet.o(i.InitialiseNetwork) refers to _printf_dec.o(.text) for _printf_int_dec
    enet.o(i.InitialiseNetwork) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    enet.o(i.InitialiseNetwork) refers to _printf_str.o(.text) for _printf_str
    enet.o(i.InitialiseNetwork) refers to noretval__2printf.o(.text) for __2printf
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    enet.o(i.InitialiseNetwork) refers to enet.o(i.enet_gpio_config) for enet_gpio_config
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(i.enet_software_reset) for enet_software_reset
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(i.enet_init) for enet_init
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(i.enet_interrupt_enable) for enet_interrupt_enable
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(i.enet_mac_address_set) for enet_mac_address_set
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(i.enet_descriptors_chain_init) for enet_descriptors_chain_init
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt) for enet_rx_desc_immediate_receive_complete_interrupt
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(i.enet_transmit_checksum_config) for enet_transmit_checksum_config
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(i.enet_enable) for enet_enable
    enet.o(i.InitialiseNetwork) refers to main.o(.data) for ucMACAddress
    enet.o(i.InitialiseNetwork) refers to gd32f4xx_enet.o(.bss) for rxdesc_tab
    enet.o(i.check_rmii_pins) refers to _printf_pad.o(.text) for _printf_pre_padding
    enet.o(i.check_rmii_pins) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    enet.o(i.check_rmii_pins) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    enet.o(i.check_rmii_pins) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    enet.o(i.check_rmii_pins) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    enet.o(i.check_rmii_pins) refers to _printf_dec.o(.text) for _printf_int_dec
    enet.o(i.check_rmii_pins) refers to noretval__2printf.o(.text) for __2printf
    enet.o(i.check_rmii_pins) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    enet.o(i.enet_gpio_config) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    enet.o(i.enet_gpio_config) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    enet.o(i.enet_gpio_config) refers to _printf_dec.o(.text) for _printf_int_dec
    enet.o(i.enet_gpio_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    enet.o(i.enet_gpio_config) refers to noretval__2printf.o(.text) for __2printf
    enet.o(i.enet_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    enet.o(i.enet_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    enet.o(i.enet_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    enet.o(i.enet_gpio_config) refers to gd32f4xx_rcu.o(i.rcu_ckout0_config) for rcu_ckout0_config
    enet.o(i.enet_gpio_config) refers to gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config) for syscfg_enet_phy_interface_config
    enet.o(i.enet_gpio_config) refers to enet.o(i.eth_rmii_gpio_conifg) for eth_rmii_gpio_conifg
    enet.o(i.enet_gpio_config) refers to enet.o(i.check_rmii_pins) for check_rmii_pins
    enet.o(i.enet_gpio_config) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    enet.o(i.eth_rmii_gpio_conifg) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    enet.o(i.eth_rmii_gpio_conifg) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    enet.o(i.eth_rmii_gpio_conifg) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    trng.o(i.trng_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    trng.o(i.trng_config) refers to gd32f4xx_trng.o(i.trng_deinit) for trng_deinit
    trng.o(i.trng_config) refers to gd32f4xx_trng.o(i.trng_enable) for trng_enable
    trng.o(i.trng_config) refers to trng.o(i.trng_ready_check) for trng_ready_check
    trng.o(i.trng_init) refers to noretval__2printf.o(.text) for __2printf
    trng.o(i.trng_init) refers to trng.o(i.trng_config) for trng_config
    trng.o(i.trng_random_range_get) refers to trng.o(i.trng_ready_check) for trng_ready_check
    trng.o(i.trng_random_range_get) refers to gd32f4xx_trng.o(i.trng_get_true_random_data) for trng_get_true_random_data
    trng.o(i.trng_ready_check) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    trng.o(i.trng_ready_check) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    trng.o(i.trng_ready_check) refers to _printf_dec.o(.text) for _printf_int_dec
    trng.o(i.trng_ready_check) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    trng.o(i.trng_ready_check) refers to noretval__2printf.o(.text) for __2printf
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to uart0.o(.data) for __stdout
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to uart0.o(.data) for __stdout
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memmove_v6.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    rt_memmove_v6.o(.text) refers to rt_memmove_w.o(.text) for __memmove_aligned
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to uart0.o(i.fputc) for fputc
    rt_memmove_w.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_gd32f450_470.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to uart0.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to uart0.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to uart0.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to uart0.o(i._ttywrch) for _ttywrch
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(i.adc_calibration_enable), (42 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_length_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_clock_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_data_alignment_config), (22 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_enable), (18 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_config), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (48 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_channel_config), (172 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_special_function_config), (90 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_routine_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_sequence_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_disable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (88 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (68 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_bit_width_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (52 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_subperipheral_select), (38 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_deinit), (164 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_get), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_flow_controller_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_mode_init), (356 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_mode_init), (344 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (34 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (104 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_reset), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_set), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (100 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_on), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_stab_wait), (348 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_current_time_get), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init), (196 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init_mode_enter), (72 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init_mode_exit), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_register_sync_wait), (96 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_bus_mode_set), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_config), (52 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_index_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_response_config), (56 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_config), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_read), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_transfer_config), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_write), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_deinit), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_set), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_response_get), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_wait_type_set), (28 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_disable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_enable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_receive), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_transmit), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_flag_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_init), (50 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_deinit), (388 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_init), (152 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_receive), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_enable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receive_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing croutine.o(.rev16_text), (4 bytes).
    Removing croutine.o(.revsh_text), (4 bytes).
    Removing event_groups.o(.rev16_text), (4 bytes).
    Removing event_groups.o(.revsh_text), (4 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (16 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (16 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (44 bytes).
    Removing event_groups.o(i.xEventGroupSync), (168 bytes).
    Removing list.o(.rev16_text), (4 bytes).
    Removing list.o(.revsh_text), (4 bytes).
    Removing queue.o(.rev16_text), (4 bytes).
    Removing queue.o(.revsh_text), (4 bytes).
    Removing queue.o(i.pcQueueGetName), (44 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (8 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (24 bytes).
    Removing queue.o(i.xQueueAddToSet), (38 bytes).
    Removing queue.o(i.xQueueCreateSet), (20 bytes).
    Removing queue.o(i.xQueueGenericSendFromISR), (180 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (152 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (16 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (22 bytes).
    Removing queue.o(i.xQueuePeek), (268 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (74 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (140 bytes).
    Removing queue.o(i.xQueueRemoveFromSet), (48 bytes).
    Removing queue.o(i.xQueueSelectFromSet), (24 bytes).
    Removing queue.o(i.xQueueSelectFromSetFromISR), (22 bytes).
    Removing stream_buffer.o(.rev16_text), (4 bytes).
    Removing stream_buffer.o(.revsh_text), (4 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (24 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (42 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (76 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (90 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (76 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (88 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (32 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (18 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (82 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (22 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (40 bytes).
    Removing stream_buffer.o(i.xStreamBufferNextMessageLengthBytes), (52 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (176 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (80 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (132 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (56 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (226 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (80 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (134 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (26 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (40 bytes).
    Removing tasks.o(.rev16_text), (4 bytes).
    Removing tasks.o(.revsh_text), (4 bytes).
    Removing tasks.o(i.eTaskGetState), (140 bytes).
    Removing tasks.o(i.pcTaskGetName), (24 bytes).
    Removing tasks.o(i.prvTaskCheckFreeStackSpace), (22 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (64 bytes).
    Removing tasks.o(i.ulTaskGenericNotifyValueClear), (64 bytes).
    Removing tasks.o(i.uxTaskGetStackHighWaterMark), (40 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (36 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (56 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (36 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (284 bytes).
    Removing tasks.o(i.vTaskResume), (172 bytes).
    Removing tasks.o(i.vTaskSuspend), (216 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (40 bytes).
    Removing tasks.o(i.xTaskDelayUntil), (116 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (332 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (504 bytes).
    Removing tasks.o(i.xTaskGenericNotifyStateClear), (64 bytes).
    Removing tasks.o(i.xTaskGenericNotifyWait), (172 bytes).
    Removing tasks.o(i.xTaskGetSchedulerState), (32 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (12 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (216 bytes).
    Removing timers.o(.rev16_text), (4 bytes).
    Removing timers.o(.revsh_text), (4 bytes).
    Removing timers.o(i.pcTimerGetName), (8 bytes).
    Removing timers.o(i.prvInitialiseNewTimer), (56 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (20 bytes).
    Removing timers.o(i.uxTimerGetReloadMode), (12 bytes).
    Removing timers.o(i.vTimerSetReloadMode), (46 bytes).
    Removing timers.o(i.vTimerSetTimerID), (20 bytes).
    Removing timers.o(i.xTimerCreate), (54 bytes).
    Removing timers.o(i.xTimerGenericCommand), (108 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (8 bytes).
    Removing timers.o(i.xTimerGetPeriod), (8 bytes).
    Removing timers.o(i.xTimerGetReloadMode), (34 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (12 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (34 bytes).
    Removing timers.o(i.xTimerPendFunctionCall), (52 bytes).
    Removing timers.o(i.xTimerPendFunctionCallFromISR), (52 bytes).
    Removing heap_4.o(.rev16_text), (4 bytes).
    Removing heap_4.o(.revsh_text), (4 bytes).
    Removing heap_4.o(i.pvPortCalloc), (54 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (128 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(.rev16_text), (4 bytes).
    Removing port.o(.revsh_text), (4 bytes).
    Removing port.o(i.vPortEndScheduler), (2 bytes).
    Removing freertos_arp.o(.rev16_text), (4 bytes).
    Removing freertos_arp.o(.revsh_text), (4 bytes).
    Removing freertos_arp.o(i.FreeRTOS_PrintARPCache), (56 bytes).
    Removing freertos_arp.o(i.vARPSendGratuitous), (20 bytes).
    Removing freertos_arp.o(i.xARPWaitResolution), (102 bytes).
    Removing freertos_arp.o(i.xCheckLoopback), (96 bytes).
    Removing freertos_dhcp.o(.rev16_text), (4 bytes).
    Removing freertos_dhcp.o(.revsh_text), (4 bytes).
    Removing freertos_dns.o(.rev16_text), (4 bytes).
    Removing freertos_dns.o(.revsh_text), (4 bytes).
    Removing freertos_dns.o(i.FreeRTOS_gethostbyname), (12 bytes).
    Removing freertos_dns.o(i.llmnr_has_dot), (30 bytes).
    Removing freertos_dns.o(i.prvCreateDNSMessage), (120 bytes).
    Removing freertos_dns.o(i.prvDNSReply), (48 bytes).
    Removing freertos_dns.o(i.prvFillSockAddress), (72 bytes).
    Removing freertos_dns.o(i.prvGetHostByName), (64 bytes).
    Removing freertos_dns.o(i.prvGetHostByNameOp), (100 bytes).
    Removing freertos_dns.o(i.prvGetHostByNameOp_WithRetry), (44 bytes).
    Removing freertos_dns.o(i.prvGetPayloadBuffer), (52 bytes).
    Removing freertos_dns.o(i.prvPrepareLookup), (180 bytes).
    Removing freertos_dns.o(i.prvSendBuffer), (116 bytes).
    Removing freertos_dns_cache.o(.rev16_text), (4 bytes).
    Removing freertos_dns_cache.o(.revsh_text), (4 bytes).
    Removing freertos_dns_cache.o(i.FreeRTOS_dnsclear), (28 bytes).
    Removing freertos_dns_cache.o(i.FreeRTOS_dnslookup), (24 bytes).
    Removing freertos_dns_callback.o(.rev16_text), (4 bytes).
    Removing freertos_dns_callback.o(.revsh_text), (4 bytes).
    Removing freertos_dns_networking.o(.rev16_text), (4 bytes).
    Removing freertos_dns_networking.o(.revsh_text), (4 bytes).
    Removing freertos_dns_networking.o(i.DNS_CloseSocket), (12 bytes).
    Removing freertos_dns_networking.o(i.DNS_CreateSocket), (102 bytes).
    Removing freertos_dns_networking.o(i.DNS_ReadReply), (38 bytes).
    Removing freertos_dns_networking.o(i.DNS_SendRequest), (44 bytes).
    Removing freertos_dns_parser.o(.rev16_text), (4 bytes).
    Removing freertos_dns_parser.o(.revsh_text), (4 bytes).
    Removing freertos_icmp.o(.rev16_text), (4 bytes).
    Removing freertos_icmp.o(.revsh_text), (4 bytes).
    Removing freertos_ip.o(.rev16_text), (4 bytes).
    Removing freertos_ip.o(.revsh_text), (4 bytes).
    Removing freertos_ip.o(i.FreeRTOS_GetDNSServerAddress), (12 bytes).
    Removing freertos_ip.o(i.FreeRTOS_GetGatewayAddress), (12 bytes).
    Removing freertos_ip.o(i.FreeRTOS_GetIPAddress), (12 bytes).
    Removing freertos_ip.o(i.FreeRTOS_GetMACAddress), (8 bytes).
    Removing freertos_ip.o(i.FreeRTOS_GetNetmask), (12 bytes).
    Removing freertos_ip.o(i.FreeRTOS_GetUDPPayloadBuffer), (56 bytes).
    Removing freertos_ip.o(i.FreeRTOS_IsNetworkUp), (12 bytes).
    Removing freertos_ip.o(i.FreeRTOS_NetworkDownFromISR), (56 bytes).
    Removing freertos_ip.o(i.FreeRTOS_ReleaseTCPPayloadBuffer), (62 bytes).
    Removing freertos_ip.o(i.FreeRTOS_ReleaseUDPPayloadBuffer), (18 bytes).
    Removing freertos_ip.o(i.FreeRTOS_SetAddressConfiguration), (44 bytes).
    Removing freertos_ip.o(i.FreeRTOS_SetGatewayAddress), (12 bytes).
    Removing freertos_ip.o(i.FreeRTOS_SetIPAddress), (12 bytes).
    Removing freertos_ip.o(i.FreeRTOS_SetNetmask), (12 bytes).
    Removing freertos_ip.o(i.FreeRTOS_UpdateMACAddress), (16 bytes).
    Removing freertos_ip.o(i.xIsNetworkDownEventPending), (12 bytes).
    Removing freertos_ip_timers.o(.rev16_text), (4 bytes).
    Removing freertos_ip_timers.o(.revsh_text), (4 bytes).
    Removing freertos_ip_utils.o(.rev16_text), (4 bytes).
    Removing freertos_ip_utils.o(.revsh_text), (4 bytes).
    Removing freertos_ip_utils.o(i.FreeRTOS_max_int32), (14 bytes).
    Removing freertos_ip_utils.o(i.FreeRTOS_max_size_t), (14 bytes).
    Removing freertos_ip_utils.o(i.FreeRTOS_round_down), (16 bytes).
    Removing freertos_ip_utils.o(i.FreeRTOS_strerror_r), (368 bytes).
    Removing freertos_sockets.o(.rev16_text), (4 bytes).
    Removing freertos_sockets.o(.revsh_text), (4 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_CreateSocketSet), (38 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_DeleteSocketSet), (30 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_EUI48_ntop), (104 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_EUI48_pton), (118 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_FD_CLR), (36 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_FD_ISSET), (26 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_FD_SET), (46 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_GetLocalAddress), (36 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_GetRemoteAddress), (74 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_connect), (192 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_connstatus), (24 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_get_rx_buf), (28 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_get_tx_head), (86 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_inet_addr), (26 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_inet_pton), (36 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_inet_pton4), (204 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_issocketconnected), (40 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_maywrite), (74 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_mss), (24 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_netstat), (24 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_recvfrom), (254 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_select), (82 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_sendto), (216 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_setsockopt), (712 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_shutdown), (72 bytes).
    Removing freertos_sockets.o(i.FreeRTOS_tx_size), (40 bytes).
    Removing freertos_sockets.o(i.bMayConnect), (66 bytes).
    Removing freertos_sockets.o(i.prvFindSelectedSocket), (92 bytes).
    Removing freertos_sockets.o(i.prvMakeSureSocketIsBound), (42 bytes).
    Removing freertos_sockets.o(i.prvSockopt_so_buffer), (200 bytes).
    Removing freertos_sockets.o(i.prvTCPConnectStart), (264 bytes).
    Removing freertos_sockets.o(i.ucASCIIToHex), (74 bytes).
    Removing freertos_sockets.o(i.xSocketValid), (14 bytes).
    Removing freertos_stream_buffer.o(.rev16_text), (4 bytes).
    Removing freertos_stream_buffer.o(.revsh_text), (4 bytes).
    Removing freertos_tcp_ip.o(.rev16_text), (4 bytes).
    Removing freertos_tcp_ip.o(.revsh_text), (4 bytes).
    Removing freertos_tcp_reception.o(.rev16_text), (4 bytes).
    Removing freertos_tcp_reception.o(.revsh_text), (4 bytes).
    Removing freertos_tcp_state_handling.o(.rev16_text), (4 bytes).
    Removing freertos_tcp_state_handling.o(.revsh_text), (4 bytes).
    Removing freertos_tcp_transmission.o(.rev16_text), (4 bytes).
    Removing freertos_tcp_transmission.o(.revsh_text), (4 bytes).
    Removing freertos_tcp_utils.o(.rev16_text), (4 bytes).
    Removing freertos_tcp_utils.o(.revsh_text), (4 bytes).
    Removing freertos_tcp_win.o(.rev16_text), (4 bytes).
    Removing freertos_tcp_win.o(.revsh_text), (4 bytes).
    Removing freertos_tcp_win.o(i.vTCPSegmentCleanup), (28 bytes).
    Removing freertos_tiny_tcp.o(.rev16_text), (4 bytes).
    Removing freertos_tiny_tcp.o(.revsh_text), (4 bytes).
    Removing freertos_udp_ip.o(.rev16_text), (4 bytes).
    Removing freertos_udp_ip.o(.revsh_text), (4 bytes).
    Removing bufferallocation_2.o(.rev16_text), (4 bytes).
    Removing bufferallocation_2.o(.revsh_text), (4 bytes).
    Removing bufferallocation_2.o(i.pucGetNetworkBuffer), (44 bytes).
    Removing bufferallocation_2.o(i.pxResizeNetworkBufferWithDescriptor), (82 bytes).
    Removing bufferallocation_2.o(i.uxGetMinimumFreeNetworkBuffers), (12 bytes).
    Removing networkinterface.o(.rev16_text), (4 bytes).
    Removing networkinterface.o(.revsh_text), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing uart0.o(.rev16_text), (4 bytes).
    Removing uart0.o(.revsh_text), (4 bytes).
    Removing uart0.o(i._ttywrch), (4 bytes).
    Removing uart0.o(i.uart0_send_data), (60 bytes).
    Removing enet.o(.rev16_text), (4 bytes).
    Removing enet.o(.revsh_text), (4 bytes).
    Removing trng.o(.rev16_text), (4 bytes).
    Removing trng.o(.revsh_text), (4 bytes).

1104 unused section(s) (total 48086 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    CMSIS\\gd32f4xx_it.c                     0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    CMSIS\\system_gd32f4xx.c                 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    CMSIS\gd32f4xx_it.c                      0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    CMSIS\startup_gd32f450_470.s             0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    CMSIS\system_gd32f4xx.c                  0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    FWLib\Source\gd32f4xx_adc.c              0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    FWLib\Source\gd32f4xx_can.c              0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    FWLib\Source\gd32f4xx_crc.c              0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    FWLib\Source\gd32f4xx_ctc.c              0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    FWLib\Source\gd32f4xx_dac.c              0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    FWLib\Source\gd32f4xx_dbg.c              0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    FWLib\Source\gd32f4xx_dci.c              0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    FWLib\Source\gd32f4xx_dma.c              0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    FWLib\Source\gd32f4xx_enet.c             0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    FWLib\Source\gd32f4xx_exmc.c             0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    FWLib\Source\gd32f4xx_exti.c             0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    FWLib\Source\gd32f4xx_fmc.c              0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    FWLib\Source\gd32f4xx_fwdgt.c            0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    FWLib\Source\gd32f4xx_gpio.c             0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    FWLib\Source\gd32f4xx_i2c.c              0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    FWLib\Source\gd32f4xx_ipa.c              0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    FWLib\Source\gd32f4xx_iref.c             0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    FWLib\Source\gd32f4xx_misc.c             0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    FWLib\Source\gd32f4xx_pmu.c              0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    FWLib\Source\gd32f4xx_rcu.c              0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    FWLib\Source\gd32f4xx_rtc.c              0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    FWLib\Source\gd32f4xx_sdio.c             0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    FWLib\Source\gd32f4xx_spi.c              0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    FWLib\Source\gd32f4xx_syscfg.c           0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    FWLib\Source\gd32f4xx_timer.c            0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    FWLib\Source\gd32f4xx_tli.c              0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    FWLib\Source\gd32f4xx_trng.c             0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    FWLib\Source\gd32f4xx_usart.c            0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    FWLib\Source\gd32f4xx_wwdgt.c            0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_adc.c            0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_can.c            0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_crc.c            0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_ctc.c            0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_dac.c            0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_dbg.c            0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_dci.c            0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_dma.c            0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_enet.c           0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_exmc.c           0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_exti.c           0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_fmc.c            0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_fwdgt.c          0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_gpio.c           0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_i2c.c            0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_ipa.c            0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_iref.c           0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_misc.c           0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_pmu.c            0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_rcu.c            0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_rtc.c            0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_sdio.c           0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_spi.c            0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_syscfg.c         0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_timer.c          0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_tli.c            0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_trng.c           0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_usart.c          0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    FWLib\\Source\\gd32f4xx_wwdgt.c          0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    Middlewares\FreeRTOS_Kernel\croutine.c   0x00000000   Number         0  croutine.o ABSOLUTE
    Middlewares\FreeRTOS_Kernel\event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    Middlewares\FreeRTOS_Kernel\list.c       0x00000000   Number         0  list.o ABSOLUTE
    Middlewares\FreeRTOS_Kernel\portable\MemMang\heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\port.c 0x00000000   Number         0  port.o ABSOLUTE
    Middlewares\FreeRTOS_Kernel\queue.c      0x00000000   Number         0  queue.o ABSOLUTE
    Middlewares\FreeRTOS_Kernel\stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    Middlewares\FreeRTOS_Kernel\tasks.c      0x00000000   Number         0  tasks.o ABSOLUTE
    Middlewares\FreeRTOS_Kernel\timers.c     0x00000000   Number         0  timers.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_ARP.c  0x00000000   Number         0  freertos_arp.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_DHCP.c 0x00000000   Number         0  freertos_dhcp.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_DNS.c  0x00000000   Number         0  freertos_dns.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Cache.c 0x00000000   Number         0  freertos_dns_cache.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Callback.c 0x00000000   Number         0  freertos_dns_callback.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Networking.c 0x00000000   Number         0  freertos_dns_networking.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Parser.c 0x00000000   Number         0  freertos_dns_parser.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_ICMP.c 0x00000000   Number         0  freertos_icmp.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_IP.c   0x00000000   Number         0  freertos_ip.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_IP_Timers.c 0x00000000   Number         0  freertos_ip_timers.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_IP_Utils.c 0x00000000   Number         0  freertos_ip_utils.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_Sockets.c 0x00000000   Number         0  freertos_sockets.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_Stream_Buffer.c 0x00000000   Number         0  freertos_stream_buffer.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_IP.c 0x00000000   Number         0  freertos_tcp_ip.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_Reception.c 0x00000000   Number         0  freertos_tcp_reception.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_State_Handling.c 0x00000000   Number         0  freertos_tcp_state_handling.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_Transmission.c 0x00000000   Number         0  freertos_tcp_transmission.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_Utils.c 0x00000000   Number         0  freertos_tcp_utils.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_WIN.c 0x00000000   Number         0  freertos_tcp_win.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_Tiny_TCP.c 0x00000000   Number         0  freertos_tiny_tcp.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\FreeRTOS_UDP_IP.c 0x00000000   Number         0  freertos_udp_ip.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\portable\BufferManagement\BufferAllocation_2.c 0x00000000   Number         0  bufferallocation_2.o ABSOLUTE
    Middlewares\FreeRTOS_TCP\portable\NetworkInterface\NetworkInterface.c 0x00000000   Number         0  networkinterface.o ABSOLUTE
    Middlewares\\FreeRTOS_Kernel\\croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    Middlewares\\FreeRTOS_Kernel\\event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    Middlewares\\FreeRTOS_Kernel\\list.c     0x00000000   Number         0  list.o ABSOLUTE
    Middlewares\\FreeRTOS_Kernel\\portable\\MemMang\\heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    Middlewares\\FreeRTOS_Kernel\\portable\\RVDS\\ARM_CM4F\\port.c 0x00000000   Number         0  port.o ABSOLUTE
    Middlewares\\FreeRTOS_Kernel\\queue.c    0x00000000   Number         0  queue.o ABSOLUTE
    Middlewares\\FreeRTOS_Kernel\\stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    Middlewares\\FreeRTOS_Kernel\\tasks.c    0x00000000   Number         0  tasks.o ABSOLUTE
    Middlewares\\FreeRTOS_Kernel\\timers.c   0x00000000   Number         0  timers.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_ARP.c 0x00000000   Number         0  freertos_arp.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_DHCP.c 0x00000000   Number         0  freertos_dhcp.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_DNS.c 0x00000000   Number         0  freertos_dns.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_DNS_Cache.c 0x00000000   Number         0  freertos_dns_cache.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_DNS_Callback.c 0x00000000   Number         0  freertos_dns_callback.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_DNS_Networking.c 0x00000000   Number         0  freertos_dns_networking.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_DNS_Parser.c 0x00000000   Number         0  freertos_dns_parser.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_ICMP.c 0x00000000   Number         0  freertos_icmp.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_IP.c 0x00000000   Number         0  freertos_ip.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_IP_Timers.c 0x00000000   Number         0  freertos_ip_timers.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_IP_Utils.c 0x00000000   Number         0  freertos_ip_utils.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_Sockets.c 0x00000000   Number         0  freertos_sockets.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_Stream_Buffer.c 0x00000000   Number         0  freertos_stream_buffer.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_TCP_IP.c 0x00000000   Number         0  freertos_tcp_ip.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_TCP_Reception.c 0x00000000   Number         0  freertos_tcp_reception.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_TCP_State_Handling.c 0x00000000   Number         0  freertos_tcp_state_handling.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_TCP_Transmission.c 0x00000000   Number         0  freertos_tcp_transmission.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_TCP_Utils.c 0x00000000   Number         0  freertos_tcp_utils.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_TCP_WIN.c 0x00000000   Number         0  freertos_tcp_win.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_Tiny_TCP.c 0x00000000   Number         0  freertos_tiny_tcp.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\FreeRTOS_UDP_IP.c 0x00000000   Number         0  freertos_udp_ip.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\portable\\BufferManagement\\BufferAllocation_2.c 0x00000000   Number         0  bufferallocation_2.o ABSOLUTE
    Middlewares\\FreeRTOS_TCP\\portable\\NetworkInterface\\NetworkInterface.c 0x00000000   Number         0  networkinterface.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    user\\enet.c                             0x00000000   Number         0  enet.o ABSOLUTE
    user\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    user\\trng.c                             0x00000000   Number         0  trng.o ABSOLUTE
    user\\uart0.c                            0x00000000   Number         0  uart0.o ABSOLUTE
    user\enet.c                              0x00000000   Number         0  enet.o ABSOLUTE
    user\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    user\trng.c                              0x00000000   Number         0  trng.o ABSOLUTE
    user\uart0.c                             0x00000000   Number         0  uart0.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001e8   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000204   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000220   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x08000220   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000226   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000C  0x0800022c   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000013  0x08000232   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000238   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800023e   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000242   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000244   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000248   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800024a   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800024c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800024c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x0800024c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x0800024c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x0800024c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x0800024c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x0800024c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x0800024c   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800024e   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800024e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800024e   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000254   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000254   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000258   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000258   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000260   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000262   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000262   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000266   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x0800026c   Section      190  port.o(.emb_text)
    $v0                                      0x0800026c   Number         0  port.o(.emb_text)
    .text                                    0x0800032c   Section       64  startup_gd32f450_470.o(.text)
    $v0                                      0x0800032c   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x0800036c   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08000370   Section        0  noretval__2printf.o(.text)
    .text                                    0x08000388   Section        0  noretval__2snprintf.o(.text)
    .text                                    0x080003bc   Section        0  _printf_pad.o(.text)
    .text                                    0x0800040a   Section        0  _printf_str.o(.text)
    .text                                    0x0800045c   Section        0  _printf_dec.o(.text)
    .text                                    0x080004d4   Section        0  _printf_hex_int.o(.text)
    .text                                    0x0800052c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x080006b4   Section        0  memcmp.o(.text)
    .text                                    0x0800070c   Section        0  strcpy.o(.text)
    .text                                    0x08000754   Section        0  strlen.o(.text)
    .text                                    0x08000792   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800081c   Section      132  rt_memmove_v6.o(.text)
    .text                                    0x080008a0   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000904   Section       16  aeabi_memset.o(.text)
    .text                                    0x08000914   Section       68  rt_memclr.o(.text)
    .text                                    0x08000958   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080009a8   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000a28   Section        0  heapauxi.o(.text)
    .text                                    0x08000a2e   Section        2  use_no_semi.o(.text)
    .text                                    0x08000a30   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000ae4   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000ae5   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000b14   Section        0  _sputc.o(.text)
    .text                                    0x08000b1e   Section        0  _snputc.o(.text)
    .text                                    0x08000b2e   Section        0  _printf_char.o(.text)
    .text                                    0x08000b5c   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000b80   Section      122  rt_memmove_w.o(.text)
    .text                                    0x08000bfa   Section        0  ferror.o(.text)
    .text                                    0x08000c02   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000c4c   Section        0  exit.o(.text)
    .text                                    0x08000c60   Section        8  libspace.o(.text)
    i.BusFault_Handler                       0x08000c68   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.DNS_ParseDNSReply                      0x08000c88   Section        0  freertos_dns_parser.o(i.DNS_ParseDNSReply)
    i.DNS_ReadNameField                      0x08000f48   Section        0  freertos_dns_parser.o(i.DNS_ReadNameField)
    i.DNS_SkipNameField                      0x08000fea   Section        0  freertos_dns_parser.o(i.DNS_SkipNameField)
    i.DNS_TreatNBNS                          0x0800103c   Section        0  freertos_dns_parser.o(i.DNS_TreatNBNS)
    i.ENET_IRQHandler                        0x08001248   Section        0  networkinterface.o(i.ENET_IRQHandler)
    i.FreeRTOS_ClearARP                      0x080012bc   Section        0  freertos_arp.o(i.FreeRTOS_ClearARP)
    i.FreeRTOS_GetAddressConfiguration       0x080012cc   Section        0  freertos_ip.o(i.FreeRTOS_GetAddressConfiguration)
    i.FreeRTOS_GetIPTaskHandle               0x080012f8   Section        0  freertos_ip.o(i.FreeRTOS_GetIPTaskHandle)
    i.FreeRTOS_GetTCPStateName               0x08001304   Section        0  freertos_tcp_state_handling.o(i.FreeRTOS_GetTCPStateName)
    i.FreeRTOS_IPInit                        0x08001320   Section        0  freertos_ip.o(i.FreeRTOS_IPInit)
    i.FreeRTOS_NetworkDown                   0x080014d0   Section        0  freertos_ip.o(i.FreeRTOS_NetworkDown)
    i.FreeRTOS_OutputARPRequest              0x080014f8   Section        0  freertos_arp.o(i.FreeRTOS_OutputARPRequest)
    i.FreeRTOS_ProcessDNSCache               0x0800153c   Section        0  freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache)
    i.FreeRTOS_accept                        0x08001630   Section        0  freertos_sockets.o(i.FreeRTOS_accept)
    i.FreeRTOS_bind                          0x08001748   Section        0  freertos_sockets.o(i.FreeRTOS_bind)
    i.FreeRTOS_closesocket                   0x08001818   Section        0  freertos_sockets.o(i.FreeRTOS_closesocket)
    i.FreeRTOS_dns_update                    0x0800186c   Section        0  freertos_dns_cache.o(i.FreeRTOS_dns_update)
    i.FreeRTOS_inet_ntoa                     0x08001884   Section        0  freertos_sockets.o(i.FreeRTOS_inet_ntoa)
    i.FreeRTOS_inet_ntop                     0x08001900   Section        0  freertos_sockets.o(i.FreeRTOS_inet_ntop)
    i.FreeRTOS_inet_ntop4                    0x0800192a   Section        0  freertos_sockets.o(i.FreeRTOS_inet_ntop4)
    i.FreeRTOS_listen                        0x08001954   Section        0  freertos_sockets.o(i.FreeRTOS_listen)
    i.FreeRTOS_max_uint32                    0x080019e4   Section        0  freertos_ip_utils.o(i.FreeRTOS_max_uint32)
    i.FreeRTOS_min_int32                     0x080019f2   Section        0  freertos_ip_utils.o(i.FreeRTOS_min_int32)
    i.FreeRTOS_min_size_t                    0x08001a00   Section        0  freertos_ip_utils.o(i.FreeRTOS_min_size_t)
    i.FreeRTOS_min_uint32                    0x08001a0e   Section        0  freertos_ip_utils.o(i.FreeRTOS_min_uint32)
    i.FreeRTOS_recv                          0x08001a1c   Section        0  freertos_sockets.o(i.FreeRTOS_recv)
    i.FreeRTOS_round_up                      0x08001b78   Section        0  freertos_ip_utils.o(i.FreeRTOS_round_up)
    i.FreeRTOS_rx_size                       0x08001b8c   Section        0  freertos_sockets.o(i.FreeRTOS_rx_size)
    i.FreeRTOS_send                          0x08001bb4   Section        0  freertos_sockets.o(i.FreeRTOS_send)
    i.FreeRTOS_socket                        0x08001cfc   Section        0  freertos_sockets.o(i.FreeRTOS_socket)
    i.FreeRTOS_tx_space                      0x08001dec   Section        0  freertos_sockets.o(i.FreeRTOS_tx_space)
    i.HardFault_Handler                      0x08001e14   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.InitialiseNetwork                      0x08001e34   Section        0  enet.o(i.InitialiseNetwork)
    i.MemManage_Handler                      0x08002914   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08002934   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.ProcessICMPPacket                      0x08002950   Section        0  freertos_icmp.o(i.ProcessICMPPacket)
    i.SysTick_Handler                        0x08002980   Section        0  port.o(i.SysTick_Handler)
    i.SystemInit                             0x080029b4   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.UsageFault_Handler                     0x08002a88   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i._is_digit                              0x08002aac   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x08002aba   Section        0  uart0.o(i._sys_exit)
    i.check_rmii_pins                        0x08002ac0   Section        0  enet.o(i.check_rmii_pins)
    check_rmii_pins                          0x08002ac1   Thumb Code   152  enet.o(i.check_rmii_pins)
    i.clock_test_task                        0x08002c94   Section        0  main.o(i.clock_test_task)
    i.eARPGetCacheEntry                      0x08002ee0   Section        0  freertos_arp.o(i.eARPGetCacheEntry)
    i.eARPProcessPacket                      0x08002f90   Section        0  freertos_arp.o(i.eARPProcessPacket)
    i.eConsiderFrameForProcessing            0x080030ec   Section        0  freertos_ip.o(i.eConsiderFrameForProcessing)
    i.enet_default_init                      0x0800314c   Section        0  gd32f4xx_enet.o(i.enet_default_init)
    enet_default_init                        0x0800314d   Thumb Code    78  gd32f4xx_enet.o(i.enet_default_init)
    i.enet_delay                             0x080031b4   Section        0  gd32f4xx_enet.o(i.enet_delay)
    enet_delay                               0x080031b5   Thumb Code    24  gd32f4xx_enet.o(i.enet_delay)
    i.enet_descriptors_chain_init            0x080031cc   Section        0  gd32f4xx_enet.o(i.enet_descriptors_chain_init)
    i.enet_enable                            0x08003294   Section        0  gd32f4xx_enet.o(i.enet_enable)
    i.enet_frame_receive                     0x080032a0   Section        0  gd32f4xx_enet.o(i.enet_frame_receive)
    i.enet_frame_transmit                    0x08003398   Section        0  gd32f4xx_enet.o(i.enet_frame_transmit)
    i.enet_gpio_config                       0x08003464   Section        0  enet.o(i.enet_gpio_config)
    enet_gpio_config                         0x08003465   Thumb Code   408  enet.o(i.enet_gpio_config)
    i.enet_init                              0x080037d8   Section        0  gd32f4xx_enet.o(i.enet_init)
    i.enet_interrupt_enable                  0x08003b3c   Section        0  gd32f4xx_enet.o(i.enet_interrupt_enable)
    i.enet_interrupt_flag_clear              0x08003b84   Section        0  gd32f4xx_enet.o(i.enet_interrupt_flag_clear)
    i.enet_interrupt_flag_get                0x08003b9c   Section        0  gd32f4xx_enet.o(i.enet_interrupt_flag_get)
    i.enet_inti_task                         0x08003bc0   Section        0  main.o(i.enet_inti_task)
    i.enet_mac_address_set                   0x08003bf4   Section        0  gd32f4xx_enet.o(i.enet_mac_address_set)
    i.enet_phy_config                        0x08003c24   Section        0  gd32f4xx_enet.o(i.enet_phy_config)
    i.enet_phy_write_read                    0x08003cfc   Section        0  gd32f4xx_enet.o(i.enet_phy_write_read)
    i.enet_rx_desc_immediate_receive_complete_interrupt 0x08003d98   Section        0  gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt)
    i.enet_rx_enable                         0x08003da4   Section        0  gd32f4xx_enet.o(i.enet_rx_enable)
    i.enet_rxframe_drop                      0x08003dc8   Section        0  gd32f4xx_enet.o(i.enet_rxframe_drop)
    i.enet_rxframe_size_get                  0x08003e74   Section        0  gd32f4xx_enet.o(i.enet_rxframe_size_get)
    i.enet_software_reset                    0x08003f0c   Section        0  gd32f4xx_enet.o(i.enet_software_reset)
    i.enet_transmit_checksum_config          0x08003f48   Section        0  gd32f4xx_enet.o(i.enet_transmit_checksum_config)
    i.enet_tx_enable                         0x08003f58   Section        0  gd32f4xx_enet.o(i.enet_tx_enable)
    i.enet_txfifo_flush                      0x08003f80   Section        0  gd32f4xx_enet.o(i.enet_txfifo_flush)
    i.eth_rece_data_task                     0x08003fb4   Section        0  networkinterface.o(i.eth_rece_data_task)
    i.eth_rmii_gpio_conifg                   0x08004024   Section        0  enet.o(i.eth_rmii_gpio_conifg)
    eth_rmii_gpio_conifg                     0x08004025   Thumb Code    42  enet.o(i.eth_rmii_gpio_conifg)
    i.ethernet_task_creation                 0x08004050   Section        0  networkinterface.o(i.ethernet_task_creation)
    i.fputc                                  0x08004104   Section        0  uart0.o(i.fputc)
    i.gpio_af_set                            0x08004128   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_input_bit_get                     0x08004186   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x08004196   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x080041e4   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.http_server_task                       0x08004228   Section        0  main.o(i.http_server_task)
    i.lTCPAddRxdata                          0x08004404   Section        0  freertos_sockets.o(i.lTCPAddRxdata)
    i.lTCPIncrementTxPosition                0x080044c0   Section        0  freertos_tcp_win.o(i.lTCPIncrementTxPosition)
    lTCPIncrementTxPosition                  0x080044c1   Thumb Code    14  freertos_tcp_win.o(i.lTCPIncrementTxPosition)
    i.lTCPWindowRxCheck                      0x080044d0   Section        0  freertos_tcp_win.o(i.lTCPWindowRxCheck)
    i.lTCPWindowTxAdd                        0x08004610   Section        0  freertos_tcp_win.o(i.lTCPWindowTxAdd)
    i.main                                   0x0800470c   Section        0  main.o(i.main)
    i.nvic_irq_enable                        0x0800478c   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x08004850   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.parseDNSAnswer                         0x08004864   Section        0  freertos_dns_parser.o(i.parseDNSAnswer)
    i.prepareReplyDNSMessage                 0x08004994   Section        0  freertos_dns_parser.o(i.prepareReplyDNSMessage)
    i.prvAddCurrentTaskToDelayedList         0x08004a38   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08004a39   Thumb Code   164  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x08004af8   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x08004af9   Thumb Code   200  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvAllowIPPacket                       0x08004bdc   Section        0  freertos_ip.o(i.prvAllowIPPacket)
    prvAllowIPPacket                         0x08004bdd   Thumb Code   260  freertos_ip.o(i.prvAllowIPPacket)
    i.prvCacheLookup                         0x08004cf0   Section        0  freertos_arp.o(i.prvCacheLookup)
    prvCacheLookup                           0x08004cf1   Thumb Code    74  freertos_arp.o(i.prvCacheLookup)
    i.prvCheckForValidListAndQueue           0x08004d40   Section        0  timers.o(i.prvCheckForValidListAndQueue)
    prvCheckForValidListAndQueue             0x08004d41   Thumb Code    72  timers.o(i.prvCheckForValidListAndQueue)
    i.prvCheckOptions                        0x08004da4   Section        0  freertos_tcp_reception.o(i.prvCheckOptions)
    i.prvCheckRxData                         0x08004e38   Section        0  freertos_tcp_reception.o(i.prvCheckRxData)
    i.prvCheckTasksWaitingTermination        0x08004ecc   Section        0  tasks.o(i.prvCheckTasksWaitingTermination)
    prvCheckTasksWaitingTermination          0x08004ecd   Thumb Code    60  tasks.o(i.prvCheckTasksWaitingTermination)
    i.prvCopyDataFromQueue                   0x08004f14   Section        0  queue.o(i.prvCopyDataFromQueue)
    prvCopyDataFromQueue                     0x08004f15   Thumb Code    42  queue.o(i.prvCopyDataFromQueue)
    i.prvCopyDataToQueue                     0x08004f3e   Section        0  queue.o(i.prvCopyDataToQueue)
    prvCopyDataToQueue                       0x08004f3f   Thumb Code   106  queue.o(i.prvCopyDataToQueue)
    i.prvCreateSectors                       0x08004fa8   Section        0  freertos_tcp_win.o(i.prvCreateSectors)
    prvCreateSectors                         0x08004fa9   Thumb Code   122  freertos_tcp_win.o(i.prvCreateSectors)
    i.prvDeleteTCB                           0x08005050   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x08005051   Thumb Code    18  tasks.o(i.prvDeleteTCB)
    i.prvDetermineSocketSize                 0x08005062   Section        0  freertos_sockets.o(i.prvDetermineSocketSize)
    prvDetermineSocketSize                   0x08005063   Thumb Code    70  freertos_sockets.o(i.prvDetermineSocketSize)
    i.prvFindEntryIndex                      0x080050a8   Section        0  freertos_dns_cache.o(i.prvFindEntryIndex)
    prvFindEntryIndex                        0x080050a9   Thumb Code    72  freertos_dns_cache.o(i.prvFindEntryIndex)
    i.prvGetCacheIPEntry                     0x080050f4   Section        0  freertos_dns_cache.o(i.prvGetCacheIPEntry)
    prvGetCacheIPEntry                       0x080050f5   Thumb Code   130  freertos_dns_cache.o(i.prvGetCacheIPEntry)
    i.prvGetNextExpireTime                   0x0800517c   Section        0  timers.o(i.prvGetNextExpireTime)
    prvGetNextExpireTime                     0x0800517d   Thumb Code    36  timers.o(i.prvGetNextExpireTime)
    i.prvGetPrivatePortNumber                0x080051a4   Section        0  freertos_sockets.o(i.prvGetPrivatePortNumber)
    prvGetPrivatePortNumber                  0x080051a5   Thumb Code   106  freertos_sockets.o(i.prvGetPrivatePortNumber)
    i.prvHandleEstablished                   0x08005218   Section        0  freertos_tcp_state_handling.o(i.prvHandleEstablished)
    prvHandleEstablished                     0x08005219   Thumb Code   506  freertos_tcp_state_handling.o(i.prvHandleEstablished)
    i.prvHandleEthernetPacket                0x080054b0   Section        0  freertos_ip.o(i.prvHandleEthernetPacket)
    prvHandleEthernetPacket                  0x080054b1   Thumb Code    12  freertos_ip.o(i.prvHandleEthernetPacket)
    i.prvHandleListen                        0x080054bc   Section        0  freertos_tcp_state_handling.o(i.prvHandleListen)
    i.prvHandleSynReceived                   0x0800561c   Section        0  freertos_tcp_state_handling.o(i.prvHandleSynReceived)
    prvHandleSynReceived                     0x0800561d   Thumb Code   398  freertos_tcp_state_handling.o(i.prvHandleSynReceived)
    i.prvHeapInit                            0x08005830   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x08005831   Thumb Code    90  heap_4.o(i.prvHeapInit)
    i.prvIPTask                              0x080058a0   Section        0  freertos_ip.o(i.prvIPTask)
    prvIPTask                                0x080058a1   Thumb Code    42  freertos_ip.o(i.prvIPTask)
    i.prvIPTimerCheck                        0x080058e4   Section        0  freertos_ip_timers.o(i.prvIPTimerCheck)
    prvIPTimerCheck                          0x080058e5   Thumb Code    72  freertos_ip_timers.o(i.prvIPTimerCheck)
    i.prvIPTimerReload                       0x0800592c   Section        0  freertos_ip_timers.o(i.prvIPTimerReload)
    prvIPTimerReload                         0x0800592d   Thumb Code    18  freertos_ip_timers.o(i.prvIPTimerReload)
    i.prvIPTimerStart                        0x0800593e   Section        0  freertos_ip_timers.o(i.prvIPTimerStart)
    prvIPTimerStart                          0x0800593f   Thumb Code    48  freertos_ip_timers.o(i.prvIPTimerStart)
    i.prvIdleTask                            0x08005970   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x08005971   Thumb Code    32  tasks.o(i.prvIdleTask)
    i.prvInitialiseNewQueue                  0x08005998   Section        0  queue.o(i.prvInitialiseNewQueue)
    prvInitialiseNewQueue                    0x08005999   Thumb Code    42  queue.o(i.prvInitialiseNewQueue)
    i.prvInitialiseNewTask                   0x080059c2   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x080059c3   Thumb Code   136  tasks.o(i.prvInitialiseNewTask)
    i.prvInitialiseTaskLists                 0x08005a4c   Section        0  tasks.o(i.prvInitialiseTaskLists)
    prvInitialiseTaskLists                   0x08005a4d   Thumb Code    70  tasks.o(i.prvInitialiseTaskLists)
    i.prvInsertBlockIntoFreeList             0x08005ab4   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x08005ab5   Thumb Code    96  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvInsertCacheEntry                    0x08005b1c   Section        0  freertos_dns_cache.o(i.prvInsertCacheEntry)
    prvInsertCacheEntry                      0x08005b1d   Thumb Code   116  freertos_dns_cache.o(i.prvInsertCacheEntry)
    i.prvInsertTimerInActiveList             0x08005b98   Section        0  timers.o(i.prvInsertTimerInActiveList)
    prvInsertTimerInActiveList               0x08005b99   Thumb Code    80  timers.o(i.prvInsertTimerInActiveList)
    i.prvIsQueueEmpty                        0x08005bf0   Section        0  queue.o(i.prvIsQueueEmpty)
    prvIsQueueEmpty                          0x08005bf1   Thumb Code    26  queue.o(i.prvIsQueueEmpty)
    i.prvIsQueueFull                         0x08005c0a   Section        0  queue.o(i.prvIsQueueFull)
    prvIsQueueFull                           0x08005c0b   Thumb Code    30  queue.o(i.prvIsQueueFull)
    i.prvNotifyQueueSetContainer             0x08005c28   Section        0  queue.o(i.prvNotifyQueueSetContainer)
    prvNotifyQueueSetContainer               0x08005c29   Thumb Code    88  queue.o(i.prvNotifyQueueSetContainer)
    i.prvPacketBuffer_to_NetworkBuffer       0x08005c80   Section        0  freertos_ip_utils.o(i.prvPacketBuffer_to_NetworkBuffer)
    prvPacketBuffer_to_NetworkBuffer         0x08005c81   Thumb Code    34  freertos_ip_utils.o(i.prvPacketBuffer_to_NetworkBuffer)
    i.prvProcessEthernetPacket               0x08005ca4   Section        0  freertos_ip.o(i.prvProcessEthernetPacket)
    prvProcessEthernetPacket                 0x08005ca5   Thumb Code   146  freertos_ip.o(i.prvProcessEthernetPacket)
    i.prvProcessExpiredTimer                 0x08005d3c   Section        0  timers.o(i.prvProcessExpiredTimer)
    prvProcessExpiredTimer                   0x08005d3d   Thumb Code    62  timers.o(i.prvProcessExpiredTimer)
    i.prvProcessICMPEchoRequest              0x08005d80   Section        0  freertos_icmp.o(i.prvProcessICMPEchoRequest)
    prvProcessICMPEchoRequest                0x08005d81   Thumb Code    80  freertos_icmp.o(i.prvProcessICMPEchoRequest)
    i.prvProcessIPEventsAndTimers            0x08005dd4   Section        0  freertos_ip.o(i.prvProcessIPEventsAndTimers)
    prvProcessIPEventsAndTimers              0x08005dd5   Thumb Code   254  freertos_ip.o(i.prvProcessIPEventsAndTimers)
    i.prvProcessIPPacket                     0x08005ee0   Section        0  freertos_ip.o(i.prvProcessIPPacket)
    prvProcessIPPacket                       0x08005ee1   Thumb Code   412  freertos_ip.o(i.prvProcessIPPacket)
    i.prvProcessNetworkDownEvent             0x08006084   Section        0  freertos_ip_utils.o(i.prvProcessNetworkDownEvent)
    i.prvProcessReceivedCommands             0x080060c4   Section        0  timers.o(i.prvProcessReceivedCommands)
    prvProcessReceivedCommands               0x080060c5   Thumb Code   268  timers.o(i.prvProcessReceivedCommands)
    i.prvProcessTimerOrBlockTask             0x080061d4   Section        0  timers.o(i.prvProcessTimerOrBlockTask)
    prvProcessTimerOrBlockTask               0x080061d5   Thumb Code   102  timers.o(i.prvProcessTimerOrBlockTask)
    i.prvReadSackOption                      0x08006248   Section        0  freertos_tcp_reception.o(i.prvReadSackOption)
    prvReadSackOption                        0x08006249   Thumb Code    94  freertos_tcp_reception.o(i.prvReadSackOption)
    i.prvReloadTimer                         0x080062a6   Section        0  timers.o(i.prvReloadTimer)
    prvReloadTimer                           0x080062a7   Thumb Code    40  timers.o(i.prvReloadTimer)
    i.prvResetNextTaskUnblockTime            0x080062d0   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x080062d1   Thumb Code    40  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvSampleTimeNow                       0x08006300   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x08006301   Thumb Code    40  timers.o(i.prvSampleTimeNow)
    i.prvSendData                            0x0800632c   Section        0  freertos_tcp_transmission.o(i.prvSendData)
    i.prvSetOptions                          0x080064c8   Section        0  freertos_tcp_transmission.o(i.prvSetOptions)
    i.prvSetSynAckOptions                    0x08006608   Section        0  freertos_tcp_transmission.o(i.prvSetSynAckOptions)
    i.prvSingleStepTCPHeaderOptions          0x08006660   Section        0  freertos_tcp_reception.o(i.prvSingleStepTCPHeaderOptions)
    prvSingleStepTCPHeaderOptions            0x08006661   Thumb Code   326  freertos_tcp_reception.o(i.prvSingleStepTCPHeaderOptions)
    i.prvSocketSetMSS                        0x080067d8   Section        0  freertos_tcp_utils.o(i.prvSocketSetMSS)
    i.prvStoreRxData                         0x0800685c   Section        0  freertos_tcp_reception.o(i.prvStoreRxData)
    i.prvSwitchTimerLists                    0x08006968   Section        0  timers.o(i.prvSwitchTimerLists)
    prvSwitchTimerLists                      0x08006969   Thumb Code    58  timers.o(i.prvSwitchTimerLists)
    i.prvTCPAddTxData                        0x080069ac   Section        0  freertos_tcp_transmission.o(i.prvTCPAddTxData)
    i.prvTCPBufferResize                     0x080069dc   Section        0  freertos_tcp_transmission.o(i.prvTCPBufferResize)
    i.prvTCPCreateStream                     0x08006a5c   Section        0  freertos_sockets.o(i.prvTCPCreateStream)
    prvTCPCreateStream                       0x08006a5d   Thumb Code   192  freertos_sockets.o(i.prvTCPCreateStream)
    i.prvTCPCreateWindow                     0x08006b84   Section        0  freertos_tcp_transmission.o(i.prvTCPCreateWindow)
    i.prvTCPFlagMeaning                      0x08006c18   Section        0  freertos_tcp_utils.o(i.prvTCPFlagMeaning)
    i.prvTCPHandleFin                        0x08006cb8   Section        0  freertos_tcp_state_handling.o(i.prvTCPHandleFin)
    prvTCPHandleFin                          0x08006cb9   Thumb Code   286  freertos_tcp_state_handling.o(i.prvTCPHandleFin)
    i.prvTCPHandleState                      0x08006e1c   Section        0  freertos_tcp_state_handling.o(i.prvTCPHandleState)
    i.prvTCPMakeSurePrepared                 0x08007014   Section        0  freertos_tcp_transmission.o(i.prvTCPMakeSurePrepared)
    prvTCPMakeSurePrepared                   0x08007015   Thumb Code    32  freertos_tcp_transmission.o(i.prvTCPMakeSurePrepared)
    i.prvTCPNextTimeout                      0x08007034   Section        0  freertos_tcp_ip.o(i.prvTCPNextTimeout)
    prvTCPNextTimeout                        0x08007035   Thumb Code   206  freertos_tcp_ip.o(i.prvTCPNextTimeout)
    i.prvTCPPrepareConnect                   0x08007130   Section        0  freertos_tcp_transmission.o(i.prvTCPPrepareConnect)
    prvTCPPrepareConnect                     0x08007131   Thumb Code   412  freertos_tcp_transmission.o(i.prvTCPPrepareConnect)
    i.prvTCPPrepareSend                      0x08007310   Section        0  freertos_tcp_transmission.o(i.prvTCPPrepareSend)
    i.prvTCPReturnPacket                     0x08007604   Section        0  freertos_tcp_transmission.o(i.prvTCPReturnPacket)
    i.prvTCPSendChallengeAck                 0x080078fc   Section        0  freertos_tcp_transmission.o(i.prvTCPSendChallengeAck)
    i.prvTCPSendCheck                        0x0800790a   Section        0  freertos_sockets.o(i.prvTCPSendCheck)
    prvTCPSendCheck                          0x0800790b   Thumb Code   112  freertos_sockets.o(i.prvTCPSendCheck)
    i.prvTCPSendPacket                       0x0800797c   Section        0  freertos_tcp_transmission.o(i.prvTCPSendPacket)
    i.prvTCPSendRepeated                     0x08007a30   Section        0  freertos_tcp_transmission.o(i.prvTCPSendRepeated)
    i.prvTCPSendReset                        0x08007a6e   Section        0  freertos_tcp_transmission.o(i.prvTCPSendReset)
    i.prvTCPSendSpecialPacketHelper          0x08007a7c   Section        0  freertos_tcp_transmission.o(i.prvTCPSendSpecialPacketHelper)
    prvTCPSendSpecialPacketHelper            0x08007a7d   Thumb Code    40  freertos_tcp_transmission.o(i.prvTCPSendSpecialPacketHelper)
    i.prvTCPSetSocketCount                   0x08007aa4   Section        0  freertos_sockets.o(i.prvTCPSetSocketCount)
    prvTCPSetSocketCount                     0x08007aa5   Thumb Code   156  freertos_sockets.o(i.prvTCPSetSocketCount)
    i.prvTCPSocketCopy                       0x08007b78   Section        0  freertos_tcp_state_handling.o(i.prvTCPSocketCopy)
    prvTCPSocketCopy                         0x08007b79   Thumb Code   184  freertos_tcp_state_handling.o(i.prvTCPSocketCopy)
    i.prvTCPSocketIsActive                   0x08007c8c   Section        0  freertos_tcp_state_handling.o(i.prvTCPSocketIsActive)
    i.prvTCPStatusAgeCheck                   0x08007cc4   Section        0  freertos_tcp_state_handling.o(i.prvTCPStatusAgeCheck)
    i.prvTCPTouchSocket                      0x08007d88   Section        0  freertos_tcp_ip.o(i.prvTCPTouchSocket)
    prvTCPTouchSocket                        0x08007d89   Thumb Code    40  freertos_tcp_ip.o(i.prvTCPTouchSocket)
    i.prvTCPWindowFastRetransmit             0x08007db0   Section        0  freertos_tcp_win.o(i.prvTCPWindowFastRetransmit)
    prvTCPWindowFastRetransmit               0x08007db1   Thumb Code   148  freertos_tcp_win.o(i.prvTCPWindowFastRetransmit)
    i.prvTCPWindowRx_ExpectedRX              0x08007e88   Section        0  freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX)
    prvTCPWindowRx_ExpectedRX                0x08007e89   Thumb Code   146  freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX)
    i.prvTCPWindowRx_UnexpectedRX            0x08007f24   Section        0  freertos_tcp_win.o(i.prvTCPWindowRx_UnexpectedRX)
    prvTCPWindowRx_UnexpectedRX              0x08007f25   Thumb Code   240  freertos_tcp_win.o(i.prvTCPWindowRx_UnexpectedRX)
    i.prvTCPWindowTxAdd_FrontSegment         0x0800808c   Section        0  freertos_tcp_win.o(i.prvTCPWindowTxAdd_FrontSegment)
    prvTCPWindowTxAdd_FrontSegment           0x0800808d   Thumb Code   102  freertos_tcp_win.o(i.prvTCPWindowTxAdd_FrontSegment)
    i.prvTCPWindowTxCheckAck                 0x080080fc   Section        0  freertos_tcp_win.o(i.prvTCPWindowTxCheckAck)
    prvTCPWindowTxCheckAck                   0x080080fd   Thumb Code   284  freertos_tcp_win.o(i.prvTCPWindowTxCheckAck)
    i.prvTCPWindowTxCheckAck_CalcSRTT        0x0800825c   Section        0  freertos_tcp_win.o(i.prvTCPWindowTxCheckAck_CalcSRTT)
    prvTCPWindowTxCheckAck_CalcSRTT          0x0800825d   Thumb Code    74  freertos_tcp_win.o(i.prvTCPWindowTxCheckAck_CalcSRTT)
    i.prvTCPWindowTxHasSpace                 0x080082a6   Section        0  freertos_tcp_win.o(i.prvTCPWindowTxHasSpace)
    prvTCPWindowTxHasSpace                   0x080082a7   Thumb Code    94  freertos_tcp_win.o(i.prvTCPWindowTxHasSpace)
    i.prvTaskExitError                       0x08008304   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08008305   Thumb Code    22  port.o(i.prvTaskExitError)
    i.prvTestWaitCondition                   0x0800831a   Section        0  event_groups.o(i.prvTestWaitCondition)
    prvTestWaitCondition                     0x0800831b   Thumb Code    30  event_groups.o(i.prvTestWaitCondition)
    i.prvTimerTask                           0x08008338   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08008339   Thumb Code    26  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x08008352   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x08008353   Thumb Code   144  queue.o(i.prvUnlockQueue)
    i.prvUpdateCacheEntry                    0x080083e4   Section        0  freertos_dns_cache.o(i.prvUpdateCacheEntry)
    prvUpdateCacheEntry                      0x080083e5   Thumb Code    44  freertos_dns_cache.o(i.prvUpdateCacheEntry)
    i.prvValidSocket                         0x08008414   Section        0  freertos_sockets.o(i.prvValidSocket)
    prvValidSocket                           0x08008415   Thumb Code    42  freertos_sockets.o(i.prvValidSocket)
    i.prvWinScaleFactor                      0x08008440   Section        0  freertos_tcp_transmission.o(i.prvWinScaleFactor)
    prvWinScaleFactor                        0x08008441   Thumb Code    52  freertos_tcp_transmission.o(i.prvWinScaleFactor)
    i.pvPortMalloc                           0x080084a8   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxDuplicateNetworkBufferWithDescriptor 0x0800858c   Section        0  freertos_ip_utils.o(i.pxDuplicateNetworkBufferWithDescriptor)
    i.pxGetNetworkBufferWithDescriptor       0x080085c8   Section        0  bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor)
    i.pxListFindListItemWithValue            0x08008664   Section        0  freertos_sockets.o(i.pxListFindListItemWithValue)
    pxListFindListItemWithValue              0x08008665   Thumb Code    48  freertos_sockets.o(i.pxListFindListItemWithValue)
    i.pxPortInitialiseStack                  0x08008694   Section        0  port.o(i.pxPortInitialiseStack)
    i.pxTCPSocketLookup                      0x080086c0   Section        0  freertos_sockets.o(i.pxTCPSocketLookup)
    i.pxTCPWindowTx_GetTXQueue               0x0800871c   Section        0  freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue)
    pxTCPWindowTx_GetTXQueue                 0x0800871d   Thumb Code   132  freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue)
    i.pxTCPWindowTx_GetWaitQueue             0x080087a8   Section        0  freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue)
    pxTCPWindowTx_GetWaitQueue               0x080087a9   Thumb Code   110  freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue)
    i.pxUDPPayloadBuffer_to_NetworkBuffer    0x08008820   Section        0  freertos_ip_utils.o(i.pxUDPPayloadBuffer_to_NetworkBuffer)
    i.pxUDPSocketLookup                      0x08008830   Section        0  freertos_sockets.o(i.pxUDPSocketLookup)
    i.rcu_ckout0_config                      0x0800884c   Section        0  gd32f4xx_rcu.o(i.rcu_ckout0_config)
    i.rcu_clock_freq_get                     0x08008868   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_periph_clock_enable                0x0800898c   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x080089b0   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x080089d4   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.start_task                             0x080089f8   Section        0  main.o(i.start_task)
    i.syscfg_enet_phy_interface_config       0x08008b2c   Section        0  gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config)
    i.system_clock_200m_25m_hxtal            0x08008b44   Section        0  system_gd32f4xx.o(i.system_clock_200m_25m_hxtal)
    system_clock_200m_25m_hxtal              0x08008b45   Thumb Code   240  system_gd32f4xx.o(i.system_clock_200m_25m_hxtal)
    i.system_clock_config                    0x08008c40   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x08008c41   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.tcp_server_task                        0x08008c48   Section        0  main.o(i.tcp_server_task)
    i.trng_config                            0x08008de8   Section        0  trng.o(i.trng_config)
    trng_config                              0x08008de9   Thumb Code    30  trng.o(i.trng_config)
    i.trng_deinit                            0x08008e06   Section        0  gd32f4xx_trng.o(i.trng_deinit)
    i.trng_enable                            0x08008e1c   Section        0  gd32f4xx_trng.o(i.trng_enable)
    i.trng_flag_get                          0x08008e30   Section        0  gd32f4xx_trng.o(i.trng_flag_get)
    i.trng_get_true_random_data              0x08008e48   Section        0  gd32f4xx_trng.o(i.trng_get_true_random_data)
    i.trng_init                              0x08008e54   Section        0  trng.o(i.trng_init)
    i.trng_random_range_get                  0x08008ea0   Section        0  trng.o(i.trng_random_range_get)
    i.trng_ready_check                       0x08008ec0   Section        0  trng.o(i.trng_ready_check)
    i.uart0_init                             0x08008f74   Section        0  uart0.o(i.uart0_init)
    i.ulApplicationGetNextSequenceNumber     0x08009000   Section        0  networkinterface.o(i.ulApplicationGetNextSequenceNumber)
    i.ulChar2u32                             0x08009014   Section        0  freertos_ip_utils.o(i.ulChar2u32)
    i.ulDNSHandlePacket                      0x0800902c   Section        0  freertos_dns.o(i.ulDNSHandlePacket)
    i.ulNBNSHandlePacket                     0x08009054   Section        0  freertos_dns.o(i.ulNBNSHandlePacket)
    i.ulTCPWindowTxAck                       0x0800907c   Section        0  freertos_tcp_win.o(i.ulTCPWindowTxAck)
    i.ulTCPWindowTxGet                       0x080090b0   Section        0  freertos_tcp_win.o(i.ulTCPWindowTxGet)
    i.ulTCPWindowTxSack                      0x080091c0   Section        0  freertos_tcp_win.o(i.ulTCPWindowTxSack)
    i.ulTaskGenericNotifyTake                0x08009254   Section        0  tasks.o(i.ulTaskGenericNotifyTake)
    i.ulTimerGetAge                          0x080092dc   Section        0  freertos_tcp_win.o(i.ulTimerGetAge)
    ulTimerGetAge                            0x080092dd   Thumb Code    18  freertos_tcp_win.o(i.ulTimerGetAge)
    i.usChar2u16                             0x080092ee   Section        0  freertos_ip_utils.o(i.usChar2u16)
    i.usGenerateChecksum                     0x080092fa   Section        0  freertos_ip_utils.o(i.usGenerateChecksum)
    i.usGenerateProtocolChecksum             0x08009484   Section        0  freertos_ip_utils.o(i.usGenerateProtocolChecksum)
    i.usart_baudrate_set                     0x08009754   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_transmit                    0x0800983c   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x08009844   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_enable                           0x08009920   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x0800992a   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_transmit_config                  0x08009948   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.uxGetNumberOfFreeNetworkBuffers        0x08009958   Section        0  bufferallocation_2.o(i.uxGetNumberOfFreeNetworkBuffers)
    i.uxListRemove                           0x08009964   Section        0  list.o(i.uxListRemove)
    i.uxQueueMessagesWaiting                 0x0800998c   Section        0  queue.o(i.uxQueueMessagesWaiting)
    i.uxRand                                 0x0800999e   Section        0  networkinterface.o(i.uxRand)
    i.uxStreamBufferAdd                      0x080099a6   Section        0  freertos_stream_buffer.o(i.uxStreamBufferAdd)
    i.uxStreamBufferDistance                 0x08009a48   Section        0  freertos_stream_buffer.o(i.uxStreamBufferDistance)
    i.uxStreamBufferFrontSpace               0x08009a5e   Section        0  freertos_stream_buffer.o(i.uxStreamBufferFrontSpace)
    i.uxStreamBufferGet                      0x08009a72   Section        0  freertos_stream_buffer.o(i.uxStreamBufferGet)
    i.uxStreamBufferGetPtr                   0x08009afe   Section        0  freertos_stream_buffer.o(i.uxStreamBufferGetPtr)
    i.uxStreamBufferGetSize                  0x08009b26   Section        0  freertos_stream_buffer.o(i.uxStreamBufferGetSize)
    i.uxStreamBufferGetSpace                 0x08009b3a   Section        0  freertos_stream_buffer.o(i.uxStreamBufferGetSpace)
    i.uxStreamBufferMidSpace                 0x08009b4e   Section        0  freertos_stream_buffer.o(i.uxStreamBufferMidSpace)
    i.uxStreamBufferSpace                    0x08009b62   Section        0  freertos_stream_buffer.o(i.uxStreamBufferSpace)
    i.uxTaskGetNumberOfTasks                 0x08009b7c   Section        0  tasks.o(i.uxTaskGetNumberOfTasks)
    i.uxTaskResetEventItemValue              0x08009b88   Section        0  tasks.o(i.uxTaskResetEventItemValue)
    i.vARPAgeCache                           0x08009ba4   Section        0  freertos_arp.o(i.vARPAgeCache)
    i.vARPGenerateRequestPacket              0x08009c60   Section        0  freertos_arp.o(i.vARPGenerateRequestPacket)
    i.vARPRefreshCacheEntry                  0x08009cac   Section        0  freertos_arp.o(i.vARPRefreshCacheEntry)
    i.vARPTimerReload                        0x08009e10   Section        0  freertos_ip_timers.o(i.vARPTimerReload)
    i.vApplicationIPNetworkEventHook         0x08009e24   Section        0  networkinterface.o(i.vApplicationIPNetworkEventHook)
    i.vCheckNetworkTimers                    0x0800a014   Section        0  freertos_ip_timers.o(i.vCheckNetworkTimers)
    i.vEventGroupDelete                      0x0800a0a8   Section        0  event_groups.o(i.vEventGroupDelete)
    i.vIPNetworkUpCalls                      0x0800a0d4   Section        0  freertos_ip.o(i.vIPNetworkUpCalls)
    i.vIPSetARPResolutionTimerEnableState    0x0800a0f0   Section        0  freertos_ip_timers.o(i.vIPSetARPResolutionTimerEnableState)
    i.vIPSetARPTimerEnableState              0x0800a114   Section        0  freertos_ip_timers.o(i.vIPSetARPTimerEnableState)
    i.vIPSetTCPTimerExpiredState             0x0800a138   Section        0  freertos_ip_timers.o(i.vIPSetTCPTimerExpiredState)
    i.vIPTimerStartARPResolution             0x0800a168   Section        0  freertos_ip_timers.o(i.vIPTimerStartARPResolution)
    i.vListInitialise                        0x0800a17c   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x0800a196   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x0800a19c   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x0800a1d0   Section        0  list.o(i.vListInsertEnd)
    i.vListInsertFifo                        0x0800a1e8   Section        0  freertos_tcp_win.o(i.vListInsertFifo)
    vListInsertFifo                          0x0800a1e9   Thumb Code    20  freertos_tcp_win.o(i.vListInsertFifo)
    i.vListInsertGeneric                     0x0800a1fc   Section        0  freertos_tcp_win.o(i.vListInsertGeneric)
    vListInsertGeneric                       0x0800a1fd   Thumb Code    22  freertos_tcp_win.o(i.vListInsertGeneric)
    i.vLoggingPrintf                         0x0800a212   Section        0  networkinterface.o(i.vLoggingPrintf)
    i.vNetworkSocketsInit                    0x0800a214   Section        0  freertos_sockets.o(i.vNetworkSocketsInit)
    i.vPortEnterCritical                     0x0800a22c   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x0800a250   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x0800a270   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x0800a2bc   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vPreCheckConfigs                       0x0800a2e4   Section        0  freertos_ip_utils.o(i.vPreCheckConfigs)
    i.vProcessARPPacketReply                 0x0800a2e8   Section        0  freertos_arp.o(i.vProcessARPPacketReply)
    vProcessARPPacketReply                   0x0800a2e9   Thumb Code   122  freertos_arp.o(i.vProcessARPPacketReply)
    i.vProcessGeneratedUDPPacket             0x0800a36c   Section        0  freertos_udp_ip.o(i.vProcessGeneratedUDPPacket)
    i.vQueueAddToRegistry                    0x0800a468   Section        0  queue.o(i.vQueueAddToRegistry)
    i.vQueueDelete                           0x0800a4b0   Section        0  queue.o(i.vQueueDelete)
    i.vQueueUnregisterQueue                  0x0800a4c4   Section        0  queue.o(i.vQueueUnregisterQueue)
    i.vQueueWaitForMessageRestricted         0x0800a4f4   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vReleaseNetworkBuffer                  0x0800a53e   Section        0  bufferallocation_2.o(i.vReleaseNetworkBuffer)
    i.vReleaseNetworkBufferAndDescriptor     0x0800a550   Section        0  bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor)
    i.vReturnEthernetFrame                   0x0800a59c   Section        0  freertos_ip.o(i.vReturnEthernetFrame)
    i.vSetMultiCastIPv4MacAddress            0x0800a5d0   Section        0  freertos_ip_utils.o(i.vSetMultiCastIPv4MacAddress)
    i.vSocketBind                            0x0800a604   Section        0  freertos_sockets.o(i.vSocketBind)
    i.vSocketClose                           0x0800a6e0   Section        0  freertos_sockets.o(i.vSocketClose)
    i.vSocketCloseNextTime                   0x0800a7bc   Section        0  freertos_tcp_ip.o(i.vSocketCloseNextTime)
    i.vSocketListenNextTime                  0x0800a7e0   Section        0  freertos_tcp_ip.o(i.vSocketListenNextTime)
    i.vSocketSelect                          0x0800a80c   Section        0  freertos_sockets.o(i.vSocketSelect)
    i.vSocketWakeUpUser                      0x0800a998   Section        0  freertos_sockets.o(i.vSocketWakeUpUser)
    i.vStreamBufferClear                     0x0800a9d8   Section        0  freertos_stream_buffer.o(i.vStreamBufferClear)
    i.vStreamBufferMoveMid                   0x0800a9e4   Section        0  freertos_stream_buffer.o(i.vStreamBufferMoveMid)
    i.vTCPStateChange                        0x0800aa10   Section        0  freertos_tcp_ip.o(i.vTCPStateChange)
    i.vTCPTimerReload                        0x0800ac4c   Section        0  freertos_ip_timers.o(i.vTCPTimerReload)
    i.vTCPTimerSet                           0x0800ac60   Section        0  freertos_tcp_win.o(i.vTCPTimerSet)
    vTCPTimerSet                             0x0800ac61   Thumb Code    12  freertos_tcp_win.o(i.vTCPTimerSet)
    i.vTCPWindowCreate                       0x0800ac6c   Section        0  freertos_tcp_win.o(i.vTCPWindowCreate)
    i.vTCPWindowDestroy                      0x0800ad0c   Section        0  freertos_tcp_win.o(i.vTCPWindowDestroy)
    i.vTCPWindowFree                         0x0800ad44   Section        0  freertos_tcp_win.o(i.vTCPWindowFree)
    vTCPWindowFree                           0x0800ad45   Thumb Code    48  freertos_tcp_win.o(i.vTCPWindowFree)
    i.vTCPWindowInit                         0x0800ad78   Section        0  freertos_tcp_win.o(i.vTCPWindowInit)
    i.vTaskDelay                             0x0800adcc   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskDelete                            0x0800ae00   Section        0  tasks.o(i.vTaskDelete)
    i.vTaskGenericNotifyGiveFromISR          0x0800aed4   Section        0  tasks.o(i.vTaskGenericNotifyGiveFromISR)
    i.vTaskInternalSetTimeOutState           0x0800b024   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskMissedYield                       0x0800b03c   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x0800b048   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x0800b068   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskPlaceOnUnorderedEventList         0x0800b0b4   Section        0  tasks.o(i.vTaskPlaceOnUnorderedEventList)
    i.vTaskRemoveFromUnorderedEventList      0x0800b104   Section        0  tasks.o(i.vTaskRemoveFromUnorderedEventList)
    i.vTaskSetTimeOutState                   0x0800b1d4   Section        0  tasks.o(i.vTaskSetTimeOutState)
    i.vTaskStartScheduler                    0x0800b1f8   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x0800b270   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x0800b280   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xApplicationDNSQueryHook               0x0800b2e0   Section        0  networkinterface.o(i.xApplicationDNSQueryHook)
    i.xApplicationGetRandomNumber            0x0800b2e6   Section        0  networkinterface.o(i.xApplicationGetRandomNumber)
    i.xCalculateSleepTime                    0x0800b2f4   Section        0  freertos_ip_timers.o(i.xCalculateSleepTime)
    i.xCheckRequiresARPResolution            0x0800b330   Section        0  freertos_arp.o(i.xCheckRequiresARPResolution)
    i.xCheckSizeFields                       0x0800b374   Section        0  freertos_ip.o(i.xCheckSizeFields)
    xCheckSizeFields                         0x0800b375   Thumb Code   164  freertos_ip.o(i.xCheckSizeFields)
    i.xEventGroupClearBits                   0x0800b418   Section        0  event_groups.o(i.xEventGroupClearBits)
    i.xEventGroupCreate                      0x0800b438   Section        0  event_groups.o(i.xEventGroupCreate)
    i.xEventGroupSetBits                     0x0800b452   Section        0  event_groups.o(i.xEventGroupSetBits)
    i.xEventGroupWaitBits                    0x0800b4e4   Section        0  event_groups.o(i.xEventGroupWaitBits)
    i.xIPIsNetworkTaskReady                  0x0800b5b0   Section        0  freertos_ip.o(i.xIPIsNetworkTaskReady)
    i.xIsCallingFromIPTask                   0x0800b5bc   Section        0  freertos_ip_utils.o(i.xIsCallingFromIPTask)
    i.xIsIPInARPCache                        0x0800b5d8   Section        0  freertos_arp.o(i.xIsIPInARPCache)
    i.xIsIPv4Multicast                       0x0800b610   Section        0  freertos_ip.o(i.xIsIPv4Multicast)
    i.xNetworkBuffersInitialise              0x0800b640   Section        0  bufferallocation_2.o(i.xNetworkBuffersInitialise)
    i.xNetworkInterfaceInitialise            0x0800b6e8   Section        0  networkinterface.o(i.xNetworkInterfaceInitialise)
    i.xNetworkInterfaceOutput                0x0800b710   Section        0  networkinterface.o(i.xNetworkInterfaceOutput)
    i.xPortStartScheduler                    0x0800b72c   Section        0  port.o(i.xPortStartScheduler)
    i.xProcessReceivedTCPPacket              0x0800b774   Section        0  freertos_tcp_ip.o(i.xProcessReceivedTCPPacket)
    i.xProcessReceivedUDPPacket              0x0800bab0   Section        0  freertos_udp_ip.o(i.xProcessReceivedUDPPacket)
    i.xQueueCreateCountingSemaphore          0x0800bb94   Section        0  queue.o(i.xQueueCreateCountingSemaphore)
    i.xQueueGenericCreate                    0x0800bbb6   Section        0  queue.o(i.xQueueGenericCreate)
    i.xQueueGenericReset                     0x0800bc04   Section        0  queue.o(i.xQueueGenericReset)
    i.xQueueGenericSend                      0x0800bca4   Section        0  queue.o(i.xQueueGenericSend)
    i.xQueueReceive                          0x0800bdec   Section        0  queue.o(i.xQueueReceive)
    i.xQueueSemaphoreTake                    0x0800bef0   Section        0  queue.o(i.xQueueSemaphoreTake)
    i.xSendEventStructToIPTask               0x0800bfe0   Section        0  freertos_ip.o(i.xSendEventStructToIPTask)
    i.xSendEventToIPTask                     0x0800c07c   Section        0  freertos_ip.o(i.xSendEventToIPTask)
    i.xSequenceGreaterThan                   0x0800c094   Section        0  freertos_tcp_win.o(i.xSequenceGreaterThan)
    i.xSequenceGreaterThanOrEqual            0x0800c0a6   Section        0  freertos_tcp_win.o(i.xSequenceGreaterThanOrEqual)
    xSequenceGreaterThanOrEqual              0x0800c0a7   Thumb Code    16  freertos_tcp_win.o(i.xSequenceGreaterThanOrEqual)
    i.xSequenceLessThan                      0x0800c0b6   Section        0  freertos_tcp_win.o(i.xSequenceLessThan)
    i.xStreamBufferLessThenEqual             0x0800c0c8   Section        0  freertos_stream_buffer.o(i.xStreamBufferLessThenEqual)
    i.xTCPCheckNewClient                     0x0800c0dc   Section        0  freertos_tcp_ip.o(i.xTCPCheckNewClient)
    i.xTCPSocketCheck                        0x0800c168   Section        0  freertos_tcp_ip.o(i.xTCPSocketCheck)
    i.xTCPTimerCheck                         0x0800c24c   Section        0  freertos_sockets.o(i.xTCPTimerCheck)
    i.xTCPWindowGetHead                      0x0800c2f0   Section        0  freertos_tcp_win.o(i.xTCPWindowGetHead)
    xTCPWindowGetHead                        0x0800c2f1   Thumb Code    34  freertos_tcp_win.o(i.xTCPWindowGetHead)
    i.xTCPWindowNew                          0x0800c314   Section        0  freertos_tcp_win.o(i.xTCPWindowNew)
    xTCPWindowNew                            0x0800c315   Thumb Code   146  freertos_tcp_win.o(i.xTCPWindowNew)
    i.xTCPWindowPeekHead                     0x0800c3e0   Section        0  freertos_tcp_win.o(i.xTCPWindowPeekHead)
    xTCPWindowPeekHead                       0x0800c3e1   Thumb Code    24  freertos_tcp_win.o(i.xTCPWindowPeekHead)
    i.xTCPWindowRxConfirm                    0x0800c3f8   Section        0  freertos_tcp_win.o(i.xTCPWindowRxConfirm)
    xTCPWindowRxConfirm                      0x0800c3f9   Thumb Code   152  freertos_tcp_win.o(i.xTCPWindowRxConfirm)
    i.xTCPWindowRxEmpty                      0x0800c4d0   Section        0  freertos_tcp_win.o(i.xTCPWindowRxEmpty)
    i.xTCPWindowRxFind                       0x0800c53c   Section        0  freertos_tcp_win.o(i.xTCPWindowRxFind)
    xTCPWindowRxFind                         0x0800c53d   Thumb Code    38  freertos_tcp_win.o(i.xTCPWindowRxFind)
    i.xTCPWindowTxDone                       0x0800c562   Section        0  freertos_tcp_win.o(i.xTCPWindowTxDone)
    i.xTCPWindowTxHasData                    0x0800c572   Section        0  freertos_tcp_win.o(i.xTCPWindowTxHasData)
    i.xTaskCheckForTimeOut                   0x0800c604   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x0800c664   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskGetCurrentTaskHandle              0x0800c6cc   Section        0  tasks.o(i.xTaskGetCurrentTaskHandle)
    i.xTaskGetTickCount                      0x0800c6d8   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x0800c6e4   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskRemoveFromEventList               0x0800c874   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x0800c97c   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x0800cad8   Section        0  timers.o(i.xTimerCreateTimerTask)
    x$fpl$fpinit                             0x0800cb18   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800cb18   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x0800cb22   Section       38  freertos_arp.o(.constdata)
    xDefaultPartARPPacketHeader              0x0800cb22   Data          38  freertos_arp.o(.constdata)
    .constdata                               0x0800cb48   Section       18  freertos_dns.o(.constdata)
    xDefaultPartDNSHeader                    0x0800cb4e   Data          12  freertos_dns.o(.constdata)
    .constdata                               0x0800cb5c   Section       24  freertos_ip.o(.constdata)
    xNetworkDownEvent                        0x0800cb64   Data           8  freertos_ip.o(.constdata)
    xNetworkDownEvent                        0x0800cb6c   Data           8  freertos_ip.o(.constdata)
    .constdata                               0x0800cb74   Section       40  freertos_sockets.o(.constdata)
    xPercTable                               0x0800cb7c   Data          32  freertos_sockets.o(.constdata)
    .constdata                               0x0800cb9c   Section       52  freertos_tcp_state_handling.o(.constdata)
    pcStateNames                             0x0800cb9c   Data          52  freertos_tcp_state_handling.o(.constdata)
    .constdata                               0x0800cbd0   Section        4  bufferallocation_2.o(.constdata)
    .constdata                               0x0800cbd4   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x0800cbd4   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x0800cbe8   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x0800cbfc   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800cbfc   Data          17  __printf_flags_ss_wp.o(.constdata)
    .conststring                             0x0800cc10   Section       76  freertos_ip_utils.o(.conststring)
    .conststring                             0x0800cc5c   Section       71  freertos_sockets.o(.conststring)
    .conststring                             0x0800cca4   Section      163  freertos_tcp_state_handling.o(.conststring)
    .conststring                             0x0800cd48   Section      508  freertos_tcp_win.o(.conststring)
    .conststring                             0x0800cf44   Section      417  main.o(.conststring)
    .data                                    0x20000000   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x20000004   Section       20  gd32f4xx_enet.o(.data)
    enet_unknow_err                          0x20000014   Data           4  gd32f4xx_enet.o(.data)
    .data                                    0x20000018   Section       64  tasks.o(.data)
    pxDelayedTaskList                        0x2000001c   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x20000020   Data           4  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x20000024   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x20000028   Data           4  tasks.o(.data)
    xTickCount                               0x2000002c   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x20000030   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x20000034   Data           4  tasks.o(.data)
    xPendedTicks                             0x20000038   Data           4  tasks.o(.data)
    xYieldPending                            0x2000003c   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x20000040   Data           4  tasks.o(.data)
    uxTaskNumber                             0x20000044   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x20000048   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x2000004c   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x20000054   Data           4  tasks.o(.data)
    .data                                    0x20000058   Section       20  timers.o(.data)
    pxCurrentTimerList                       0x20000058   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x2000005c   Data           4  timers.o(.data)
    xTimerQueue                              0x20000060   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x20000064   Data           4  timers.o(.data)
    xLastTime                                0x20000068   Data           4  timers.o(.data)
    .data                                    0x2000006c   Section       28  heap_4.o(.data)
    xStart                                   0x2000006c   Data           8  heap_4.o(.data)
    pxEnd                                    0x20000074   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x20000078   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x2000007c   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x20000080   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x20000084   Data           4  heap_4.o(.data)
    .data                                    0x20000088   Section        4  port.o(.data)
    uxCriticalNesting                        0x20000088   Data           4  port.o(.data)
    .data                                    0x2000008c   Section       20  freertos_arp.o(.data)
    xLastGratuitousARPTime                   0x2000008c   Data           4  freertos_arp.o(.data)
    uxARPClashTimeoutPeriod                  0x20000090   Data           4  freertos_arp.o(.data)
    uxARPClashCounter                        0x20000094   Data           4  freertos_arp.o(.data)
    xARPClashTimeOut                         0x20000098   Data           8  freertos_arp.o(.data)
    .data                                    0x200000a0   Section        4  freertos_dns_cache.o(.data)
    uxFreeEntry                              0x200000a0   Data           4  freertos_dns_cache.o(.data)
    .data                                    0x200000a4   Section       32  freertos_ip.o(.data)
    xNetworkDownEventPending                 0x200000b4   Data           4  freertos_ip.o(.data)
    xIPTaskHandle                            0x200000b8   Data           4  freertos_ip.o(.data)
    xNetworkUp                               0x200000bc   Data           4  freertos_ip.o(.data)
    xIPTaskInitialised                       0x200000c0   Data           4  freertos_ip.o(.data)
    .data                                    0x200000c4   Section        4  freertos_ip_utils.o(.data)
    xCallEventHook                           0x200000c4   Data           4  freertos_ip_utils.o(.data)
    .data                                    0x200000c8   Section        4  freertos_sockets.o(.data)
    xLastTime                                0x200000c8   Data           4  freertos_sockets.o(.data)
    .data                                    0x200000cc   Section        8  freertos_tcp_ip.o(.data)
    xSocketToClose                           0x200000cc   Data           4  freertos_tcp_ip.o(.data)
    xSocketToListen                          0x200000d0   Data           4  freertos_tcp_ip.o(.data)
    .data                                    0x200000d4   Section       12  freertos_tcp_win.o(.data)
    xTCPSegments                             0x200000d4   Data           4  freertos_tcp_win.o(.data)
    xLowestLength                            0x200000dc   Data           4  freertos_tcp_win.o(.data)
    .data                                    0x200000e0   Section       24  freertos_udp_ip.o(.data)
    .data                                    0x200000f8   Section        8  bufferallocation_2.o(.data)
    uxMinimumFreeNetworkBuffers              0x200000f8   Data           4  bufferallocation_2.o(.data)
    xNetworkBufferSemaphore                  0x200000fc   Data           4  bufferallocation_2.o(.data)
    .data                                    0x20000100   Section       12  networkinterface.o(.data)
    xTasksAlreadyCreated                     0x20000108   Data           4  networkinterface.o(.data)
    .data                                    0x2000010c   Section       22  main.o(.data)
    .data                                    0x20000124   Section        4  uart0.o(.data)
    .bss                                     0x20000128   Section    15460  gd32f4xx_enet.o(.bss)
    enet_initpara                            0x20003d50   Data          60  gd32f4xx_enet.o(.bss)
    .bss                                     0x20003d8c   Section      256  queue.o(.bss)
    .bss                                     0x20003e8c   Section      740  tasks.o(.bss)
    pxReadyTasksLists                        0x20003e8c   Data         640  tasks.o(.bss)
    xDelayedTaskList1                        0x2000410c   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x20004120   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x20004134   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x20004148   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x2000415c   Data          20  tasks.o(.bss)
    .bss                                     0x20004170   Section       40  timers.o(.bss)
    xActiveTimerList1                        0x20004170   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x20004184   Data          20  timers.o(.bss)
    .bss                                     0x20004198   Section    122880  heap_4.o(.bss)
    ucHeap                                   0x20004198   Data       122880  heap_4.o(.bss)
    .bss                                     0x20022198   Section       72  freertos_arp.o(.bss)
    xARPCache                                0x20022198   Data          72  freertos_arp.o(.bss)
    .bss                                     0x200221e0   Section      112  freertos_dns_cache.o(.bss)
    xDNSCache                                0x200221e0   Data         112  freertos_dns_cache.o(.bss)
    .bss                                     0x20022250   Section       40  freertos_ip.o(.bss)
    .bss                                     0x20022278   Section       60  freertos_ip_timers.o(.bss)
    xARPResolutionTimer                      0x20022278   Data          20  freertos_ip_timers.o(.bss)
    xARPTimer                                0x2002228c   Data          20  freertos_ip_timers.o(.bss)
    xTCPTimer                                0x200222a0   Data          20  freertos_ip_timers.o(.bss)
    .bss                                     0x200222b4   Section       40  freertos_sockets.o(.bss)
    .bss                                     0x200222dc   Section       10  freertos_tcp_utils.o(.bss)
    retString                                0x200222dc   Data          10  freertos_tcp_utils.o(.bss)
    .bss                                     0x200222e8   Section       20  freertos_tcp_win.o(.bss)
    xSegmentList                             0x200222e8   Data          20  freertos_tcp_win.o(.bss)
    .bss                                     0x200222fc   Section     2180  bufferallocation_2.o(.bss)
    xFreeBuffersList                         0x200222fc   Data          20  bufferallocation_2.o(.bss)
    xNetworkBufferDescriptors                0x20022310   Data        2160  bufferallocation_2.o(.bss)
    .bss                                     0x20022b80   Section       96  libspace.o(.bss)
    HEAP                                     0x20022be0   Section     1024  startup_gd32f450_470.o(HEAP)
    Heap_Mem                                 0x20022be0   Data        1024  startup_gd32f450_470.o(HEAP)
    STACK                                    0x20022fe0   Section     1024  startup_gd32f450_470.o(STACK)
    Stack_Mem                                0x20022fe0   Data        1024  startup_gd32f450_470.o(STACK)
    __initial_sp                             0x200233e0   Data           0  startup_gd32f450_470.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001e9   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000205   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x08000221   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x08000221   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_u                                0x08000227   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_x                                0x0800022d   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_c                                0x08000233   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000239   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800023f   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000243   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000245   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x0800024b   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800024d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x0800024d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800024d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x0800024d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x0800024d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x0800024d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x0800024d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x0800024d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800024f   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800024f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800024f   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000255   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000255   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000259   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000259   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000261   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000263   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000263   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000267   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    SVC_Handler                              0x0800026d   Thumb Code    28  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x0800028d   Thumb Code    36  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvEnableVFP  0x080002b5   Thumb Code    16  port.o(.emb_text)
    PendSV_Handler                           0x080002c9   Thumb Code    88  port.o(.emb_text)
    vPortGetIPSR                             0x08000325   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x0800032d   Thumb Code     8  startup_gd32f450_470.o(.text)
    DebugMon_Handler                         0x08000341   Thumb Code     2  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI10_15_IRQHandler                     0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    SDIO_IRQHandler                          0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER3_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER6_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART0_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x08000347   Thumb Code     0  startup_gd32f450_470.o(.text)
    __user_initial_stackheap                 0x08000349   Thumb Code    10  startup_gd32f450_470.o(.text)
    __use_no_semihosting                     0x0800036d   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08000371   Thumb Code    20  noretval__2printf.o(.text)
    __2snprintf                              0x08000389   Thumb Code    48  noretval__2snprintf.o(.text)
    _printf_pre_padding                      0x080003bd   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080003e9   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x0800040b   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x0800045d   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x080004d5   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x080004d5   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x0800052d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    memcmp                                   0x080006b5   Thumb Code    88  memcmp.o(.text)
    strcpy                                   0x0800070d   Thumb Code    72  strcpy.o(.text)
    strlen                                   0x08000755   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy                           0x08000793   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000793   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080007f9   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memmove                          0x0800081d   Thumb Code     0  rt_memmove_v6.o(.text)
    __rt_memmove                             0x0800081d   Thumb Code   132  rt_memmove_v6.o(.text)
    __memmove_lastfew                        0x0800087d   Thumb Code     0  rt_memmove_v6.o(.text)
    __aeabi_memcpy4                          0x080008a1   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080008a1   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080008a1   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080008e9   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memset                           0x08000905   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr                           0x08000915   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000915   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000919   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000959   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000959   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000959   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800095d   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x080009a9   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000a29   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000a2b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000a2d   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000a2f   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000a2f   Thumb Code     2  use_no_semi.o(.text)
    _printf_int_common                       0x08000a31   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x08000aef   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000b15   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000b1f   Thumb Code    16  _snputc.o(.text)
    _printf_cs_common                        0x08000b2f   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000b43   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000b53   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08000b5d   Thumb Code    32  _printf_char_file.o(.text)
    __aeabi_memmove4                         0x08000b81   Thumb Code     0  rt_memmove_w.o(.text)
    __aeabi_memmove8                         0x08000b81   Thumb Code     0  rt_memmove_w.o(.text)
    __rt_memmove_w                           0x08000b81   Thumb Code   122  rt_memmove_w.o(.text)
    __memmove_aligned                        0x08000ba7   Thumb Code     0  rt_memmove_w.o(.text)
    __memmove_lastfew_aligned                0x08000bdf   Thumb Code     0  rt_memmove_w.o(.text)
    ferror                                   0x08000bfb   Thumb Code     8  ferror.o(.text)
    __user_setup_stackheap                   0x08000c03   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000c4d   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000c61   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000c61   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000c61   Thumb Code     0  libspace.o(.text)
    BusFault_Handler                         0x08000c69   Thumb Code    10  gd32f4xx_it.o(i.BusFault_Handler)
    DNS_ParseDNSReply                        0x08000c89   Thumb Code   694  freertos_dns_parser.o(i.DNS_ParseDNSReply)
    DNS_ReadNameField                        0x08000f49   Thumb Code   162  freertos_dns_parser.o(i.DNS_ReadNameField)
    DNS_SkipNameField                        0x08000feb   Thumb Code    80  freertos_dns_parser.o(i.DNS_SkipNameField)
    DNS_TreatNBNS                            0x0800103d   Thumb Code   514  freertos_dns_parser.o(i.DNS_TreatNBNS)
    ENET_IRQHandler                          0x08001249   Thumb Code   102  networkinterface.o(i.ENET_IRQHandler)
    FreeRTOS_ClearARP                        0x080012bd   Thumb Code    12  freertos_arp.o(i.FreeRTOS_ClearARP)
    FreeRTOS_GetAddressConfiguration         0x080012cd   Thumb Code    36  freertos_ip.o(i.FreeRTOS_GetAddressConfiguration)
    FreeRTOS_GetIPTaskHandle                 0x080012f9   Thumb Code     6  freertos_ip.o(i.FreeRTOS_GetIPTaskHandle)
    FreeRTOS_GetTCPStateName                 0x08001305   Thumb Code    24  freertos_tcp_state_handling.o(i.FreeRTOS_GetTCPStateName)
    FreeRTOS_IPInit                          0x08001321   Thumb Code   274  freertos_ip.o(i.FreeRTOS_IPInit)
    FreeRTOS_NetworkDown                     0x080014d1   Thumb Code    32  freertos_ip.o(i.FreeRTOS_NetworkDown)
    FreeRTOS_OutputARPRequest                0x080014f9   Thumb Code    68  freertos_arp.o(i.FreeRTOS_OutputARPRequest)
    FreeRTOS_ProcessDNSCache                 0x0800153d   Thumb Code   178  freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache)
    FreeRTOS_accept                          0x08001631   Thumb Code   280  freertos_sockets.o(i.FreeRTOS_accept)
    FreeRTOS_bind                            0x08001749   Thumb Code   128  freertos_sockets.o(i.FreeRTOS_bind)
    FreeRTOS_closesocket                     0x08001819   Thumb Code    52  freertos_sockets.o(i.FreeRTOS_closesocket)
    FreeRTOS_dns_update                      0x0800186d   Thumb Code    24  freertos_dns_cache.o(i.FreeRTOS_dns_update)
    FreeRTOS_inet_ntoa                       0x08001885   Thumb Code   124  freertos_sockets.o(i.FreeRTOS_inet_ntoa)
    FreeRTOS_inet_ntop                       0x08001901   Thumb Code    42  freertos_sockets.o(i.FreeRTOS_inet_ntop)
    FreeRTOS_inet_ntop4                      0x0800192b   Thumb Code    42  freertos_sockets.o(i.FreeRTOS_inet_ntop4)
    FreeRTOS_listen                          0x08001955   Thumb Code   144  freertos_sockets.o(i.FreeRTOS_listen)
    FreeRTOS_max_uint32                      0x080019e5   Thumb Code    14  freertos_ip_utils.o(i.FreeRTOS_max_uint32)
    FreeRTOS_min_int32                       0x080019f3   Thumb Code    14  freertos_ip_utils.o(i.FreeRTOS_min_int32)
    FreeRTOS_min_size_t                      0x08001a01   Thumb Code    14  freertos_ip_utils.o(i.FreeRTOS_min_size_t)
    FreeRTOS_min_uint32                      0x08001a0f   Thumb Code    14  freertos_ip_utils.o(i.FreeRTOS_min_uint32)
    FreeRTOS_recv                            0x08001a1d   Thumb Code   348  freertos_sockets.o(i.FreeRTOS_recv)
    FreeRTOS_round_up                        0x08001b79   Thumb Code    20  freertos_ip_utils.o(i.FreeRTOS_round_up)
    FreeRTOS_rx_size                         0x08001b8d   Thumb Code    40  freertos_sockets.o(i.FreeRTOS_rx_size)
    FreeRTOS_send                            0x08001bb5   Thumb Code   286  freertos_sockets.o(i.FreeRTOS_send)
    FreeRTOS_socket                          0x08001cfd   Thumb Code   240  freertos_sockets.o(i.FreeRTOS_socket)
    FreeRTOS_tx_space                        0x08001ded   Thumb Code    40  freertos_sockets.o(i.FreeRTOS_tx_space)
    HardFault_Handler                        0x08001e15   Thumb Code    10  gd32f4xx_it.o(i.HardFault_Handler)
    InitialiseNetwork                        0x08001e35   Thumb Code  2056  enet.o(i.InitialiseNetwork)
    MemManage_Handler                        0x08002915   Thumb Code    10  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08002935   Thumb Code    10  gd32f4xx_it.o(i.NMI_Handler)
    ProcessICMPPacket                        0x08002951   Thumb Code    48  freertos_icmp.o(i.ProcessICMPPacket)
    SysTick_Handler                          0x08002981   Thumb Code    46  port.o(i.SysTick_Handler)
    SystemInit                               0x080029b5   Thumb Code   194  system_gd32f4xx.o(i.SystemInit)
    UsageFault_Handler                       0x08002a89   Thumb Code    10  gd32f4xx_it.o(i.UsageFault_Handler)
    _is_digit                                0x08002aad   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x08002abb   Thumb Code     4  uart0.o(i._sys_exit)
    clock_test_task                          0x08002c95   Thumb Code   184  main.o(i.clock_test_task)
    eARPGetCacheEntry                        0x08002ee1   Thumb Code   162  freertos_arp.o(i.eARPGetCacheEntry)
    eARPProcessPacket                        0x08002f91   Thumb Code   328  freertos_arp.o(i.eARPProcessPacket)
    eConsiderFrameForProcessing              0x080030ed   Thumb Code    82  freertos_ip.o(i.eConsiderFrameForProcessing)
    enet_descriptors_chain_init              0x080031cd   Thumb Code   162  gd32f4xx_enet.o(i.enet_descriptors_chain_init)
    enet_enable                              0x08003295   Thumb Code    12  gd32f4xx_enet.o(i.enet_enable)
    enet_frame_receive                       0x080032a1   Thumb Code   236  gd32f4xx_enet.o(i.enet_frame_receive)
    enet_frame_transmit                      0x08003399   Thumb Code   196  gd32f4xx_enet.o(i.enet_frame_transmit)
    enet_init                                0x080037d9   Thumb Code   822  gd32f4xx_enet.o(i.enet_init)
    enet_interrupt_enable                    0x08003b3d   Thumb Code    66  gd32f4xx_enet.o(i.enet_interrupt_enable)
    enet_interrupt_flag_clear                0x08003b85   Thumb Code    18  gd32f4xx_enet.o(i.enet_interrupt_flag_clear)
    enet_interrupt_flag_get                  0x08003b9d   Thumb Code    30  gd32f4xx_enet.o(i.enet_interrupt_flag_get)
    enet_inti_task                           0x08003bc1   Thumb Code    32  main.o(i.enet_inti_task)
    enet_mac_address_set                     0x08003bf5   Thumb Code    42  gd32f4xx_enet.o(i.enet_mac_address_set)
    enet_phy_config                          0x08003c25   Thumb Code   184  gd32f4xx_enet.o(i.enet_phy_config)
    enet_phy_write_read                      0x08003cfd   Thumb Code   148  gd32f4xx_enet.o(i.enet_phy_write_read)
    enet_rx_desc_immediate_receive_complete_interrupt 0x08003d99   Thumb Code    10  gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt)
    enet_rx_enable                           0x08003da5   Thumb Code    26  gd32f4xx_enet.o(i.enet_rx_enable)
    enet_rxframe_drop                        0x08003dc9   Thumb Code   158  gd32f4xx_enet.o(i.enet_rxframe_drop)
    enet_rxframe_size_get                    0x08003e75   Thumb Code   138  gd32f4xx_enet.o(i.enet_rxframe_size_get)
    enet_software_reset                      0x08003f0d   Thumb Code    52  gd32f4xx_enet.o(i.enet_software_reset)
    enet_transmit_checksum_config            0x08003f49   Thumb Code    16  gd32f4xx_enet.o(i.enet_transmit_checksum_config)
    enet_tx_enable                           0x08003f59   Thumb Code    32  gd32f4xx_enet.o(i.enet_tx_enable)
    enet_txfifo_flush                        0x08003f81   Thumb Code    44  gd32f4xx_enet.o(i.enet_txfifo_flush)
    eth_rece_data_task                       0x08003fb5   Thumb Code    96  networkinterface.o(i.eth_rece_data_task)
    ethernet_task_creation                   0x08004051   Thumb Code    58  networkinterface.o(i.ethernet_task_creation)
    fputc                                    0x08004105   Thumb Code    32  uart0.o(i.fputc)
    gpio_af_set                              0x08004129   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_input_bit_get                       0x08004187   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x08004197   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x080041e5   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    http_server_task                         0x08004229   Thumb Code   220  main.o(i.http_server_task)
    lTCPAddRxdata                            0x08004405   Thumb Code   184  freertos_sockets.o(i.lTCPAddRxdata)
    lTCPWindowRxCheck                        0x080044d1   Thumb Code   186  freertos_tcp_win.o(i.lTCPWindowRxCheck)
    lTCPWindowTxAdd                          0x08004611   Thumb Code   190  freertos_tcp_win.o(i.lTCPWindowTxAdd)
    main                                     0x0800470d   Thumb Code    72  main.o(i.main)
    nvic_irq_enable                          0x0800478d   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x08004851   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    parseDNSAnswer                           0x08004865   Thumb Code   302  freertos_dns_parser.o(i.parseDNSAnswer)
    prepareReplyDNSMessage                   0x08004995   Thumb Code   154  freertos_dns_parser.o(i.prepareReplyDNSMessage)
    prvCheckOptions                          0x08004da5   Thumb Code   148  freertos_tcp_reception.o(i.prvCheckOptions)
    prvCheckRxData                           0x08004e39   Thumb Code   146  freertos_tcp_reception.o(i.prvCheckRxData)
    prvHandleListen                          0x080054bd   Thumb Code   314  freertos_tcp_state_handling.o(i.prvHandleListen)
    prvProcessNetworkDownEvent               0x08006085   Thumb Code    58  freertos_ip_utils.o(i.prvProcessNetworkDownEvent)
    prvSendData                              0x0800632d   Thumb Code   308  freertos_tcp_transmission.o(i.prvSendData)
    prvSetOptions                            0x080064c9   Thumb Code   254  freertos_tcp_transmission.o(i.prvSetOptions)
    prvSetSynAckOptions                      0x08006609   Thumb Code    88  freertos_tcp_transmission.o(i.prvSetSynAckOptions)
    prvSocketSetMSS                          0x080067d9   Thumb Code    84  freertos_tcp_utils.o(i.prvSocketSetMSS)
    prvStoreRxData                           0x0800685d   Thumb Code   226  freertos_tcp_reception.o(i.prvStoreRxData)
    prvTCPAddTxData                          0x080069ad   Thumb Code    48  freertos_tcp_transmission.o(i.prvTCPAddTxData)
    prvTCPBufferResize                       0x080069dd   Thumb Code   124  freertos_tcp_transmission.o(i.prvTCPBufferResize)
    prvTCPCreateWindow                       0x08006b85   Thumb Code    86  freertos_tcp_transmission.o(i.prvTCPCreateWindow)
    prvTCPFlagMeaning                        0x08006c19   Thumb Code   136  freertos_tcp_utils.o(i.prvTCPFlagMeaning)
    prvTCPHandleState                        0x08006e1d   Thumb Code   440  freertos_tcp_state_handling.o(i.prvTCPHandleState)
    prvTCPPrepareSend                        0x08007311   Thumb Code   596  freertos_tcp_transmission.o(i.prvTCPPrepareSend)
    prvTCPReturnPacket                       0x08007605   Thumb Code   720  freertos_tcp_transmission.o(i.prvTCPReturnPacket)
    prvTCPSendChallengeAck                   0x080078fd   Thumb Code    14  freertos_tcp_transmission.o(i.prvTCPSendChallengeAck)
    prvTCPSendPacket                         0x0800797d   Thumb Code   150  freertos_tcp_transmission.o(i.prvTCPSendPacket)
    prvTCPSendRepeated                       0x08007a31   Thumb Code    62  freertos_tcp_transmission.o(i.prvTCPSendRepeated)
    prvTCPSendReset                          0x08007a6f   Thumb Code    14  freertos_tcp_transmission.o(i.prvTCPSendReset)
    prvTCPSocketIsActive                     0x08007c8d   Thumb Code    56  freertos_tcp_state_handling.o(i.prvTCPSocketIsActive)
    prvTCPStatusAgeCheck                     0x08007cc5   Thumb Code   138  freertos_tcp_state_handling.o(i.prvTCPStatusAgeCheck)
    pvPortMalloc                             0x080084a9   Thumb Code   208  heap_4.o(i.pvPortMalloc)
    pxDuplicateNetworkBufferWithDescriptor   0x0800858d   Thumb Code    60  freertos_ip_utils.o(i.pxDuplicateNetworkBufferWithDescriptor)
    pxGetNetworkBufferWithDescriptor         0x080085c9   Thumb Code   142  bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor)
    pxPortInitialiseStack                    0x08008695   Thumb Code    38  port.o(i.pxPortInitialiseStack)
    pxTCPSocketLookup                        0x080086c1   Thumb Code    88  freertos_sockets.o(i.pxTCPSocketLookup)
    pxUDPPayloadBuffer_to_NetworkBuffer      0x08008821   Thumb Code    14  freertos_ip_utils.o(i.pxUDPPayloadBuffer_to_NetworkBuffer)
    pxUDPSocketLookup                        0x08008831   Thumb Code    24  freertos_sockets.o(i.pxUDPSocketLookup)
    rcu_ckout0_config                        0x0800884d   Thumb Code    22  gd32f4xx_rcu.o(i.rcu_ckout0_config)
    rcu_clock_freq_get                       0x08008869   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_periph_clock_enable                  0x0800898d   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x080089b1   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x080089d5   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    start_task                               0x080089f9   Thumb Code   128  main.o(i.start_task)
    syscfg_enet_phy_interface_config         0x08008b2d   Thumb Code    18  gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config)
    tcp_server_task                          0x08008c49   Thumb Code   232  main.o(i.tcp_server_task)
    trng_deinit                              0x08008e07   Thumb Code    20  gd32f4xx_trng.o(i.trng_deinit)
    trng_enable                              0x08008e1d   Thumb Code    14  gd32f4xx_trng.o(i.trng_enable)
    trng_flag_get                            0x08008e31   Thumb Code    18  gd32f4xx_trng.o(i.trng_flag_get)
    trng_get_true_random_data                0x08008e49   Thumb Code     6  gd32f4xx_trng.o(i.trng_get_true_random_data)
    trng_init                                0x08008e55   Thumb Code    34  trng.o(i.trng_init)
    trng_random_range_get                    0x08008ea1   Thumb Code    30  trng.o(i.trng_random_range_get)
    trng_ready_check                         0x08008ec1   Thumb Code    78  trng.o(i.trng_ready_check)
    uart0_init                               0x08008f75   Thumb Code   130  uart0.o(i.uart0_init)
    ulApplicationGetNextSequenceNumber       0x08009001   Thumb Code    20  networkinterface.o(i.ulApplicationGetNextSequenceNumber)
    ulChar2u32                               0x08009015   Thumb Code    24  freertos_ip_utils.o(i.ulChar2u32)
    ulDNSHandlePacket                        0x0800902d   Thumb Code    40  freertos_dns.o(i.ulDNSHandlePacket)
    ulNBNSHandlePacket                       0x08009055   Thumb Code    40  freertos_dns.o(i.ulNBNSHandlePacket)
    ulTCPWindowTxAck                         0x0800907d   Thumb Code    52  freertos_tcp_win.o(i.ulTCPWindowTxAck)
    ulTCPWindowTxGet                         0x080090b1   Thumb Code   208  freertos_tcp_win.o(i.ulTCPWindowTxGet)
    ulTCPWindowTxSack                        0x080091c1   Thumb Code    90  freertos_tcp_win.o(i.ulTCPWindowTxSack)
    ulTaskGenericNotifyTake                  0x08009255   Thumb Code   128  tasks.o(i.ulTaskGenericNotifyTake)
    usChar2u16                               0x080092ef   Thumb Code    12  freertos_ip_utils.o(i.usChar2u16)
    usGenerateChecksum                       0x080092fb   Thumb Code   394  freertos_ip_utils.o(i.usGenerateChecksum)
    usGenerateProtocolChecksum               0x08009485   Thumb Code   638  freertos_ip_utils.o(i.usGenerateProtocolChecksum)
    usart_baudrate_set                       0x08009755   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_transmit                      0x0800983d   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x08009845   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_enable                             0x08009921   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x0800992b   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_transmit_config                    0x08009949   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    uxGetNumberOfFreeNetworkBuffers          0x08009959   Thumb Code     6  bufferallocation_2.o(i.uxGetNumberOfFreeNetworkBuffers)
    uxListRemove                             0x08009965   Thumb Code    40  list.o(i.uxListRemove)
    uxQueueMessagesWaiting                   0x0800998d   Thumb Code    18  queue.o(i.uxQueueMessagesWaiting)
    uxRand                                   0x0800999f   Thumb Code     8  networkinterface.o(i.uxRand)
    uxStreamBufferAdd                        0x080099a7   Thumb Code   162  freertos_stream_buffer.o(i.uxStreamBufferAdd)
    uxStreamBufferDistance                   0x08009a49   Thumb Code    22  freertos_stream_buffer.o(i.uxStreamBufferDistance)
    uxStreamBufferFrontSpace                 0x08009a5f   Thumb Code    20  freertos_stream_buffer.o(i.uxStreamBufferFrontSpace)
    uxStreamBufferGet                        0x08009a73   Thumb Code   140  freertos_stream_buffer.o(i.uxStreamBufferGet)
    uxStreamBufferGetPtr                     0x08009aff   Thumb Code    40  freertos_stream_buffer.o(i.uxStreamBufferGetPtr)
    uxStreamBufferGetSize                    0x08009b27   Thumb Code    20  freertos_stream_buffer.o(i.uxStreamBufferGetSize)
    uxStreamBufferGetSpace                   0x08009b3b   Thumb Code    20  freertos_stream_buffer.o(i.uxStreamBufferGetSpace)
    uxStreamBufferMidSpace                   0x08009b4f   Thumb Code    20  freertos_stream_buffer.o(i.uxStreamBufferMidSpace)
    uxStreamBufferSpace                      0x08009b63   Thumb Code    24  freertos_stream_buffer.o(i.uxStreamBufferSpace)
    uxTaskGetNumberOfTasks                   0x08009b7d   Thumb Code     6  tasks.o(i.uxTaskGetNumberOfTasks)
    uxTaskResetEventItemValue                0x08009b89   Thumb Code    24  tasks.o(i.uxTaskResetEventItemValue)
    vARPAgeCache                             0x08009ba5   Thumb Code   174  freertos_arp.o(i.vARPAgeCache)
    vARPGenerateRequestPacket                0x08009c61   Thumb Code    68  freertos_arp.o(i.vARPGenerateRequestPacket)
    vARPRefreshCacheEntry                    0x08009cad   Thumb Code   342  freertos_arp.o(i.vARPRefreshCacheEntry)
    vARPTimerReload                          0x08009e11   Thumb Code    14  freertos_ip_timers.o(i.vARPTimerReload)
    vApplicationIPNetworkEventHook           0x08009e25   Thumb Code   210  networkinterface.o(i.vApplicationIPNetworkEventHook)
    vCheckNetworkTimers                      0x0800a015   Thumb Code   122  freertos_ip_timers.o(i.vCheckNetworkTimers)
    vEventGroupDelete                        0x0800a0a9   Thumb Code    42  event_groups.o(i.vEventGroupDelete)
    vIPNetworkUpCalls                        0x0800a0d5   Thumb Code    24  freertos_ip.o(i.vIPNetworkUpCalls)
    vIPSetARPResolutionTimerEnableState      0x0800a0f1   Thumb Code    32  freertos_ip_timers.o(i.vIPSetARPResolutionTimerEnableState)
    vIPSetARPTimerEnableState                0x0800a115   Thumb Code    32  freertos_ip_timers.o(i.vIPSetARPTimerEnableState)
    vIPSetTCPTimerExpiredState               0x0800a139   Thumb Code    44  freertos_ip_timers.o(i.vIPSetTCPTimerExpiredState)
    vIPTimerStartARPResolution               0x0800a169   Thumb Code    14  freertos_ip_timers.o(i.vIPTimerStartARPResolution)
    vListInitialise                          0x0800a17d   Thumb Code    26  list.o(i.vListInitialise)
    vListInitialiseItem                      0x0800a197   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x0800a19d   Thumb Code    52  list.o(i.vListInsert)
    vListInsertEnd                           0x0800a1d1   Thumb Code    24  list.o(i.vListInsertEnd)
    vLoggingPrintf                           0x0800a213   Thumb Code     2  networkinterface.o(i.vLoggingPrintf)
    vNetworkSocketsInit                      0x0800a215   Thumb Code    16  freertos_sockets.o(i.vNetworkSocketsInit)
    vPortEnterCritical                       0x0800a22d   Thumb Code    30  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x0800a251   Thumb Code    28  port.o(i.vPortExitCritical)
    vPortFree                                0x0800a271   Thumb Code    68  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x0800a2bd   Thumb Code    36  port.o(i.vPortSetupTimerInterrupt)
    vPreCheckConfigs                         0x0800a2e5   Thumb Code     2  freertos_ip_utils.o(i.vPreCheckConfigs)
    vProcessGeneratedUDPPacket               0x0800a36d   Thumb Code   242  freertos_udp_ip.o(i.vProcessGeneratedUDPPacket)
    vQueueAddToRegistry                      0x0800a469   Thumb Code    66  queue.o(i.vQueueAddToRegistry)
    vQueueDelete                             0x0800a4b1   Thumb Code    20  queue.o(i.vQueueDelete)
    vQueueUnregisterQueue                    0x0800a4c5   Thumb Code    44  queue.o(i.vQueueUnregisterQueue)
    vQueueWaitForMessageRestricted           0x0800a4f5   Thumb Code    74  queue.o(i.vQueueWaitForMessageRestricted)
    vReleaseNetworkBuffer                    0x0800a53f   Thumb Code    18  bufferallocation_2.o(i.vReleaseNetworkBuffer)
    vReleaseNetworkBufferAndDescriptor       0x0800a551   Thumb Code    68  bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor)
    vReturnEthernetFrame                     0x0800a59d   Thumb Code    46  freertos_ip.o(i.vReturnEthernetFrame)
    vSetMultiCastIPv4MacAddress              0x0800a5d1   Thumb Code    50  freertos_ip_utils.o(i.vSetMultiCastIPv4MacAddress)
    vSocketBind                              0x0800a605   Thumb Code   166  freertos_sockets.o(i.vSocketBind)
    vSocketClose                             0x0800a6e1   Thumb Code   154  freertos_sockets.o(i.vSocketClose)
    vSocketCloseNextTime                     0x0800a7bd   Thumb Code    32  freertos_tcp_ip.o(i.vSocketCloseNextTime)
    vSocketListenNextTime                    0x0800a7e1   Thumb Code    40  freertos_tcp_ip.o(i.vSocketListenNextTime)
    vSocketSelect                            0x0800a80d   Thumb Code   388  freertos_sockets.o(i.vSocketSelect)
    vSocketWakeUpUser                        0x0800a999   Thumb Code    64  freertos_sockets.o(i.vSocketWakeUpUser)
    vStreamBufferClear                       0x0800a9d9   Thumb Code    12  freertos_stream_buffer.o(i.vStreamBufferClear)
    vStreamBufferMoveMid                     0x0800a9e5   Thumb Code    44  freertos_stream_buffer.o(i.vStreamBufferMoveMid)
    vTCPStateChange                          0x0800aa11   Thumb Code   438  freertos_tcp_ip.o(i.vTCPStateChange)
    vTCPTimerReload                          0x0800ac4d   Thumb Code    14  freertos_ip_timers.o(i.vTCPTimerReload)
    vTCPWindowCreate                         0x0800ac6d   Thumb Code   102  freertos_tcp_win.o(i.vTCPWindowCreate)
    vTCPWindowDestroy                        0x0800ad0d   Thumb Code    56  freertos_tcp_win.o(i.vTCPWindowDestroy)
    vTCPWindowInit                           0x0800ad79   Thumb Code    84  freertos_tcp_win.o(i.vTCPWindowInit)
    vTaskDelay                               0x0800adcd   Thumb Code    46  tasks.o(i.vTaskDelay)
    vTaskDelete                              0x0800ae01   Thumb Code   174  tasks.o(i.vTaskDelete)
    vTaskGenericNotifyGiveFromISR            0x0800aed5   Thumb Code   310  tasks.o(i.vTaskGenericNotifyGiveFromISR)
    vTaskInternalSetTimeOutState             0x0800b025   Thumb Code    14  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x0800b03d   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x0800b049   Thumb Code    28  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x0800b069   Thumb Code    70  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskPlaceOnUnorderedEventList           0x0800b0b5   Thumb Code    74  tasks.o(i.vTaskPlaceOnUnorderedEventList)
    vTaskRemoveFromUnorderedEventList        0x0800b105   Thumb Code   190  tasks.o(i.vTaskRemoveFromUnorderedEventList)
    vTaskSetTimeOutState                     0x0800b1d5   Thumb Code    26  tasks.o(i.vTaskSetTimeOutState)
    vTaskStartScheduler                      0x0800b1f9   Thumb Code    86  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x0800b271   Thumb Code    12  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x0800b281   Thumb Code    76  tasks.o(i.vTaskSwitchContext)
    xApplicationDNSQueryHook                 0x0800b2e1   Thumb Code     6  networkinterface.o(i.xApplicationDNSQueryHook)
    xApplicationGetRandomNumber              0x0800b2e7   Thumb Code    14  networkinterface.o(i.xApplicationGetRandomNumber)
    xCalculateSleepTime                      0x0800b2f5   Thumb Code    50  freertos_ip_timers.o(i.xCalculateSleepTime)
    xCheckRequiresARPResolution              0x0800b331   Thumb Code    58  freertos_arp.o(i.xCheckRequiresARPResolution)
    xEventGroupClearBits                     0x0800b419   Thumb Code    32  event_groups.o(i.xEventGroupClearBits)
    xEventGroupCreate                        0x0800b439   Thumb Code    26  event_groups.o(i.xEventGroupCreate)
    xEventGroupSetBits                       0x0800b453   Thumb Code   146  event_groups.o(i.xEventGroupSetBits)
    xEventGroupWaitBits                      0x0800b4e5   Thumb Code   198  event_groups.o(i.xEventGroupWaitBits)
    xIPIsNetworkTaskReady                    0x0800b5b1   Thumb Code     6  freertos_ip.o(i.xIPIsNetworkTaskReady)
    xIsCallingFromIPTask                     0x0800b5bd   Thumb Code    28  freertos_ip_utils.o(i.xIsCallingFromIPTask)
    xIsIPInARPCache                          0x0800b5d9   Thumb Code    52  freertos_arp.o(i.xIsIPInARPCache)
    xIsIPv4Multicast                         0x0800b611   Thumb Code    46  freertos_ip.o(i.xIsIPv4Multicast)
    xNetworkBuffersInitialise                0x0800b641   Thumb Code   138  bufferallocation_2.o(i.xNetworkBuffersInitialise)
    xNetworkInterfaceInitialise              0x0800b6e9   Thumb Code    22  networkinterface.o(i.xNetworkInterfaceInitialise)
    xNetworkInterfaceOutput                  0x0800b711   Thumb Code    26  networkinterface.o(i.xNetworkInterfaceOutput)
    xPortStartScheduler                      0x0800b72d   Thumb Code    58  port.o(i.xPortStartScheduler)
    xProcessReceivedTCPPacket                0x0800b775   Thumb Code   648  freertos_tcp_ip.o(i.xProcessReceivedTCPPacket)
    xProcessReceivedUDPPacket                0x0800bab1   Thumb Code   222  freertos_udp_ip.o(i.xProcessReceivedUDPPacket)
    xQueueCreateCountingSemaphore            0x0800bb95   Thumb Code    34  queue.o(i.xQueueCreateCountingSemaphore)
    xQueueGenericCreate                      0x0800bbb7   Thumb Code    76  queue.o(i.xQueueGenericCreate)
    xQueueGenericReset                       0x0800bc05   Thumb Code   156  queue.o(i.xQueueGenericReset)
    xQueueGenericSend                        0x0800bca5   Thumb Code   322  queue.o(i.xQueueGenericSend)
    xQueueReceive                            0x0800bded   Thumb Code   256  queue.o(i.xQueueReceive)
    xQueueSemaphoreTake                      0x0800bef1   Thumb Code   234  queue.o(i.xQueueSemaphoreTake)
    xSendEventStructToIPTask                 0x0800bfe1   Thumb Code   108  freertos_ip.o(i.xSendEventStructToIPTask)
    xSendEventToIPTask                       0x0800c07d   Thumb Code    24  freertos_ip.o(i.xSendEventToIPTask)
    xSequenceGreaterThan                     0x0800c095   Thumb Code    18  freertos_tcp_win.o(i.xSequenceGreaterThan)
    xSequenceLessThan                        0x0800c0b7   Thumb Code    18  freertos_tcp_win.o(i.xSequenceLessThan)
    xStreamBufferLessThenEqual               0x0800c0c9   Thumb Code    20  freertos_stream_buffer.o(i.xStreamBufferLessThenEqual)
    xTCPCheckNewClient                       0x0800c0dd   Thumb Code    92  freertos_tcp_ip.o(i.xTCPCheckNewClient)
    xTCPSocketCheck                          0x0800c169   Thumb Code   180  freertos_tcp_ip.o(i.xTCPSocketCheck)
    xTCPTimerCheck                           0x0800c24d   Thumb Code   154  freertos_sockets.o(i.xTCPTimerCheck)
    xTCPWindowRxEmpty                        0x0800c4d1   Thumb Code    60  freertos_tcp_win.o(i.xTCPWindowRxEmpty)
    xTCPWindowTxDone                         0x0800c563   Thumb Code    16  freertos_tcp_win.o(i.xTCPWindowTxDone)
    xTCPWindowTxHasData                      0x0800c573   Thumb Code   144  freertos_tcp_win.o(i.xTCPWindowTxHasData)
    xTaskCheckForTimeOut                     0x0800c605   Thumb Code    88  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x0800c665   Thumb Code   104  tasks.o(i.xTaskCreate)
    xTaskGetCurrentTaskHandle                0x0800c6cd   Thumb Code     6  tasks.o(i.xTaskGetCurrentTaskHandle)
    xTaskGetTickCount                        0x0800c6d9   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x0800c6e5   Thumb Code   356  tasks.o(i.xTaskIncrementTick)
    xTaskRemoveFromEventList                 0x0800c875   Thumb Code   240  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x0800c97d   Thumb Code   310  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x0800cad9   Thumb Code    42  timers.o(i.xTimerCreateTimerTask)
    _fp_init                                 0x0800cb19   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800cb21   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800cb21   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    xLLMNR_MacAdress                         0x0800cb48   Data           6  freertos_dns.o(.constdata)
    xBroadcastMACAddress                     0x0800cb5c   Data           6  freertos_ip.o(.constdata)
    xBufferAllocFixedSize                    0x0800cbd0   Data           4  bufferallocation_2.o(.constdata)
    Region$$Table$$Base                      0x0800d0e8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800d108   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_gd32f4xx.o(.data)
    dma_current_txdesc                       0x20000004   Data           4  gd32f4xx_enet.o(.data)
    dma_current_rxdesc                       0x20000008   Data           4  gd32f4xx_enet.o(.data)
    dma_current_ptp_txdesc                   0x2000000c   Data           4  gd32f4xx_enet.o(.data)
    dma_current_ptp_rxdesc                   0x20000010   Data           4  gd32f4xx_enet.o(.data)
    pxCurrentTCB                             0x20000018   Data           4  tasks.o(.data)
    uxTopUsedPriority                        0x20000050   Data           4  tasks.o(.data)
    xProcessedTCPMessage                     0x200000a4   Data           4  freertos_ip.o(.data)
    pxARPWaitingNetworkBuffer                0x200000a8   Data           4  freertos_ip.o(.data)
    xNetworkEventQueue                       0x200000ac   Data           4  freertos_ip.o(.data)
    usPacketIdentifier                       0x200000b0   Data           2  freertos_ip.o(.data)
    xTCPWindowLoggingLevel                   0x200000d8   Data           4  freertos_tcp_win.o(.data)
    xDefaultPartUDPPacketHeader              0x200000e0   Data          24  freertos_udp_ip.o(.data)
    xEMACTaskHandle                          0x20000100   Data           4  networkinterface.o(.data)
    xPingReplyQueue                          0x20000104   Data           4  networkinterface.o(.data)
    ucMACAddress                             0x2000010c   Data           6  main.o(.data)
    ucIPAddress                              0x20000112   Data           4  main.o(.data)
    ucNetMask                                0x20000116   Data           4  main.o(.data)
    ucGatewayAddress                         0x2000011a   Data           4  main.o(.data)
    ucDNSServerAddress                       0x2000011e   Data           4  main.o(.data)
    __stdout                                 0x20000124   Data           4  uart0.o(.data)
    rxdesc_tab                               0x20000128   Data          80  gd32f4xx_enet.o(.bss)
    txdesc_tab                               0x20000178   Data          80  gd32f4xx_enet.o(.bss)
    rx_buff                                  0x200001c8   Data        7620  gd32f4xx_enet.o(.bss)
    tx_buff                                  0x20001f8c   Data        7620  gd32f4xx_enet.o(.bss)
    xQueueRegistry                           0x20003d8c   Data         256  queue.o(.bss)
    xNetworkAddressing                       0x20022250   Data          20  freertos_ip.o(.bss)
    xDefaultAddressing                       0x20022264   Data          20  freertos_ip.o(.bss)
    xBoundUDPSocketsList                     0x200222b4   Data          20  freertos_sockets.o(.bss)
    xBoundTCPSocketsList                     0x200222c8   Data          20  freertos_sockets.o(.bss)
    __libspace_start                         0x20022b80   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20022be0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000d230, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000d108, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO          288    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         9879  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO        10055    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x0000001a   Code   RO        10057    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000202   0x08000202   0x00000002   PAD
    0x08000204   0x08000204   0x0000001c   Code   RO        10059    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000220   0x08000220   0x00000000   Code   RO         9856    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000220   0x08000220   0x00000006   Code   RO         9854    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000226   0x08000226   0x00000006   Code   RO         9855    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800022c   0x0800022c   0x00000006   Code   RO         9853    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000232   0x08000232   0x00000006   Code   RO         9851    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000238   0x08000238   0x00000006   Code   RO         9852    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800023e   0x0800023e   0x00000004   Code   RO         9896    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000242   0x08000242   0x00000002   Code   RO         9929    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000244   0x08000244   0x00000004   Code   RO         9935    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9938    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9941    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9943    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9945    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9948    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9950    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9952    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9954    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9956    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9958    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9960    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9962    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9964    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9966    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9968    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9972    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9974    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9976    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO         9978    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000002   Code   RO         9979    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800024a   0x0800024a   0x00000002   Code   RO         9997    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO        10007    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO        10009    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO        10011    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO        10014    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO        10017    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO        10019    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x0800024c   0x0800024c   0x00000000   Code   RO        10022    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x0800024c   0x0800024c   0x00000002   Code   RO        10023    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800024e   0x0800024e   0x00000000   Code   RO         9883    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800024e   0x0800024e   0x00000000   Code   RO         9900    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         9912    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000254   0x08000254   0x00000000   Code   RO         9902    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000254   0x08000254   0x00000004   Code   RO         9903    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000258   0x08000258   0x00000000   Code   RO         9905    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000258   0x08000258   0x00000008   Code   RO         9906    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000260   0x08000260   0x00000002   Code   RO         9933    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000262   0x08000262   0x00000000   Code   RO         9981    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000262   0x08000262   0x00000004   Code   RO         9982    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000266   0x08000266   0x00000006   Code   RO         9983    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800026c   0x0800026c   0x000000be   Code   RO         6955    .emb_text           port.o
    0x0800032a   0x0800032a   0x00000002   PAD
    0x0800032c   0x0800032c   0x00000040   Code   RO          289    .text               startup_gd32f450_470.o
    0x0800036c   0x0800036c   0x00000002   Code   RO         9792    .text               c_w.l(use_no_semi_2.o)
    0x0800036e   0x0800036e   0x00000002   PAD
    0x08000370   0x08000370   0x00000018   Code   RO         9798    .text               c_w.l(noretval__2printf.o)
    0x08000388   0x08000388   0x00000034   Code   RO         9800    .text               c_w.l(noretval__2snprintf.o)
    0x080003bc   0x080003bc   0x0000004e   Code   RO         9804    .text               c_w.l(_printf_pad.o)
    0x0800040a   0x0800040a   0x00000052   Code   RO         9806    .text               c_w.l(_printf_str.o)
    0x0800045c   0x0800045c   0x00000078   Code   RO         9808    .text               c_w.l(_printf_dec.o)
    0x080004d4   0x080004d4   0x00000058   Code   RO         9813    .text               c_w.l(_printf_hex_int.o)
    0x0800052c   0x0800052c   0x00000188   Code   RO         9848    .text               c_w.l(__printf_flags_ss_wp.o)
    0x080006b4   0x080006b4   0x00000058   Code   RO         9857    .text               c_w.l(memcmp.o)
    0x0800070c   0x0800070c   0x00000048   Code   RO         9859    .text               c_w.l(strcpy.o)
    0x08000754   0x08000754   0x0000003e   Code   RO         9861    .text               c_w.l(strlen.o)
    0x08000792   0x08000792   0x0000008a   Code   RO         9863    .text               c_w.l(rt_memcpy_v6.o)
    0x0800081c   0x0800081c   0x00000084   Code   RO         9865    .text               c_w.l(rt_memmove_v6.o)
    0x080008a0   0x080008a0   0x00000064   Code   RO         9867    .text               c_w.l(rt_memcpy_w.o)
    0x08000904   0x08000904   0x00000010   Code   RO         9869    .text               c_w.l(aeabi_memset.o)
    0x08000914   0x08000914   0x00000044   Code   RO         9871    .text               c_w.l(rt_memclr.o)
    0x08000958   0x08000958   0x0000004e   Code   RO         9873    .text               c_w.l(rt_memclr_w.o)
    0x080009a6   0x080009a6   0x00000002   PAD
    0x080009a8   0x080009a8   0x00000080   Code   RO         9875    .text               c_w.l(strcmpv7m.o)
    0x08000a28   0x08000a28   0x00000006   Code   RO         9877    .text               c_w.l(heapauxi.o)
    0x08000a2e   0x08000a2e   0x00000002   Code   RO         9881    .text               c_w.l(use_no_semi.o)
    0x08000a30   0x08000a30   0x000000b2   Code   RO         9884    .text               c_w.l(_printf_intcommon.o)
    0x08000ae2   0x08000ae2   0x00000002   PAD
    0x08000ae4   0x08000ae4   0x00000030   Code   RO         9886    .text               c_w.l(_printf_char_common.o)
    0x08000b14   0x08000b14   0x0000000a   Code   RO         9888    .text               c_w.l(_sputc.o)
    0x08000b1e   0x08000b1e   0x00000010   Code   RO         9890    .text               c_w.l(_snputc.o)
    0x08000b2e   0x08000b2e   0x0000002c   Code   RO         9892    .text               c_w.l(_printf_char.o)
    0x08000b5a   0x08000b5a   0x00000002   PAD
    0x08000b5c   0x08000b5c   0x00000024   Code   RO         9894    .text               c_w.l(_printf_char_file.o)
    0x08000b80   0x08000b80   0x0000007a   Code   RO         9897    .text               c_w.l(rt_memmove_w.o)
    0x08000bfa   0x08000bfa   0x00000008   Code   RO         9916    .text               c_w.l(ferror.o)
    0x08000c02   0x08000c02   0x0000004a   Code   RO         9920    .text               c_w.l(sys_stackheap_outer.o)
    0x08000c4c   0x08000c4c   0x00000012   Code   RO         9922    .text               c_w.l(exit.o)
    0x08000c5e   0x08000c5e   0x00000002   PAD
    0x08000c60   0x08000c60   0x00000008   Code   RO         9930    .text               c_w.l(libspace.o)
    0x08000c68   0x08000c68   0x00000020   Code   RO            3    i.BusFault_Handler  gd32f4xx_it.o
    0x08000c88   0x08000c88   0x000002c0   Code   RO         7479    i.DNS_ParseDNSReply  freertos_dns_parser.o
    0x08000f48   0x08000f48   0x000000a2   Code   RO         7480    i.DNS_ReadNameField  freertos_dns_parser.o
    0x08000fea   0x08000fea   0x00000050   Code   RO         7481    i.DNS_SkipNameField  freertos_dns_parser.o
    0x0800103a   0x0800103a   0x00000002   PAD
    0x0800103c   0x0800103c   0x0000020c   Code   RO         7482    i.DNS_TreatNBNS     freertos_dns_parser.o
    0x08001248   0x08001248   0x00000074   Code   RO         9493    i.ENET_IRQHandler   networkinterface.o
    0x080012bc   0x080012bc   0x00000010   Code   RO         7034    i.FreeRTOS_ClearARP  freertos_arp.o
    0x080012cc   0x080012cc   0x0000002c   Code   RO         7595    i.FreeRTOS_GetAddressConfiguration  freertos_ip.o
    0x080012f8   0x080012f8   0x0000000c   Code   RO         7599    i.FreeRTOS_GetIPTaskHandle  freertos_ip.o
    0x08001304   0x08001304   0x0000001c   Code   RO         8808    i.FreeRTOS_GetTCPStateName  freertos_tcp_state_handling.o
    0x08001320   0x08001320   0x000001b0   Code   RO         7603    i.FreeRTOS_IPInit   freertos_ip.o
    0x080014d0   0x080014d0   0x00000028   Code   RO         7605    i.FreeRTOS_NetworkDown  freertos_ip.o
    0x080014f8   0x080014f8   0x00000044   Code   RO         7035    i.FreeRTOS_OutputARPRequest  freertos_arp.o
    0x0800153c   0x0800153c   0x000000f4   Code   RO         7355    i.FreeRTOS_ProcessDNSCache  freertos_dns_cache.o
    0x08001630   0x08001630   0x00000118   Code   RO         8136    i.FreeRTOS_accept   freertos_sockets.o
    0x08001748   0x08001748   0x000000d0   Code   RO         8137    i.FreeRTOS_bind     freertos_sockets.o
    0x08001818   0x08001818   0x00000054   Code   RO         8138    i.FreeRTOS_closesocket  freertos_sockets.o
    0x0800186c   0x0800186c   0x00000018   Code   RO         7356    i.FreeRTOS_dns_update  freertos_dns_cache.o
    0x08001884   0x08001884   0x0000007c   Code   RO         8144    i.FreeRTOS_inet_ntoa  freertos_sockets.o
    0x08001900   0x08001900   0x0000002a   Code   RO         8145    i.FreeRTOS_inet_ntop  freertos_sockets.o
    0x0800192a   0x0800192a   0x0000002a   Code   RO         8146    i.FreeRTOS_inet_ntop4  freertos_sockets.o
    0x08001954   0x08001954   0x00000090   Code   RO         8150    i.FreeRTOS_listen   freertos_sockets.o
    0x080019e4   0x080019e4   0x0000000e   Code   RO         7959    i.FreeRTOS_max_uint32  freertos_ip_utils.o
    0x080019f2   0x080019f2   0x0000000e   Code   RO         7960    i.FreeRTOS_min_int32  freertos_ip_utils.o
    0x08001a00   0x08001a00   0x0000000e   Code   RO         7961    i.FreeRTOS_min_size_t  freertos_ip_utils.o
    0x08001a0e   0x08001a0e   0x0000000e   Code   RO         7962    i.FreeRTOS_min_uint32  freertos_ip_utils.o
    0x08001a1c   0x08001a1c   0x0000015c   Code   RO         8154    i.FreeRTOS_recv     freertos_sockets.o
    0x08001b78   0x08001b78   0x00000014   Code   RO         7964    i.FreeRTOS_round_up  freertos_ip_utils.o
    0x08001b8c   0x08001b8c   0x00000028   Code   RO         8156    i.FreeRTOS_rx_size  freertos_sockets.o
    0x08001bb4   0x08001bb4   0x00000148   Code   RO         8158    i.FreeRTOS_send     freertos_sockets.o
    0x08001cfc   0x08001cfc   0x000000f0   Code   RO         8162    i.FreeRTOS_socket   freertos_sockets.o
    0x08001dec   0x08001dec   0x00000028   Code   RO         8164    i.FreeRTOS_tx_space  freertos_sockets.o
    0x08001e14   0x08001e14   0x00000020   Code   RO            4    i.HardFault_Handler  gd32f4xx_it.o
    0x08001e34   0x08001e34   0x00000ae0   Code   RO         9708    i.InitialiseNetwork  enet.o
    0x08002914   0x08002914   0x00000020   Code   RO            5    i.MemManage_Handler  gd32f4xx_it.o
    0x08002934   0x08002934   0x0000001c   Code   RO            6    i.NMI_Handler       gd32f4xx_it.o
    0x08002950   0x08002950   0x00000030   Code   RO         7533    i.ProcessICMPPacket  freertos_icmp.o
    0x08002980   0x08002980   0x00000034   Code   RO         6956    i.SysTick_Handler   port.o
    0x080029b4   0x080029b4   0x000000d4   Code   RO          296    i.SystemInit        system_gd32f4xx.o
    0x08002a88   0x08002a88   0x00000024   Code   RO            7    i.UsageFault_Handler  gd32f4xx_it.o
    0x08002aac   0x08002aac   0x0000000e   Code   RO         9841    i._is_digit         c_w.l(__printf_wp.o)
    0x08002aba   0x08002aba   0x00000004   Code   RO         9653    i._sys_exit         uart0.o
    0x08002abe   0x08002abe   0x00000002   PAD
    0x08002ac0   0x08002ac0   0x000001d4   Code   RO         9709    i.check_rmii_pins   enet.o
    0x08002c94   0x08002c94   0x0000024c   Code   RO         9586    i.clock_test_task   main.o
    0x08002ee0   0x08002ee0   0x000000b0   Code   RO         7037    i.eARPGetCacheEntry  freertos_arp.o
    0x08002f90   0x08002f90   0x0000015c   Code   RO         7038    i.eARPProcessPacket  freertos_arp.o
    0x080030ec   0x080030ec   0x00000060   Code   RO         7614    i.eConsiderFrameForProcessing  freertos_ip.o
    0x0800314c   0x0800314c   0x00000068   Code   RO         1647    i.enet_default_init  gd32f4xx_enet.o
    0x080031b4   0x080031b4   0x00000018   Code   RO         1649    i.enet_delay        gd32f4xx_enet.o
    0x080031cc   0x080031cc   0x000000c8   Code   RO         1655    i.enet_descriptors_chain_init  gd32f4xx_enet.o
    0x08003294   0x08003294   0x0000000c   Code   RO         1662    i.enet_enable       gd32f4xx_enet.o
    0x080032a0   0x080032a0   0x000000f8   Code   RO         1672    i.enet_frame_receive  gd32f4xx_enet.o
    0x08003398   0x08003398   0x000000cc   Code   RO         1673    i.enet_frame_transmit  gd32f4xx_enet.o
    0x08003464   0x08003464   0x00000374   Code   RO         9710    i.enet_gpio_config  enet.o
    0x080037d8   0x080037d8   0x00000364   Code   RO         1674    i.enet_init         gd32f4xx_enet.o
    0x08003b3c   0x08003b3c   0x00000048   Code   RO         1678    i.enet_interrupt_enable  gd32f4xx_enet.o
    0x08003b84   0x08003b84   0x00000018   Code   RO         1679    i.enet_interrupt_flag_clear  gd32f4xx_enet.o
    0x08003b9c   0x08003b9c   0x00000024   Code   RO         1680    i.enet_interrupt_flag_get  gd32f4xx_enet.o
    0x08003bc0   0x08003bc0   0x00000034   Code   RO         9587    i.enet_inti_task    main.o
    0x08003bf4   0x08003bf4   0x00000030   Code   RO         1682    i.enet_mac_address_set  gd32f4xx_enet.o
    0x08003c24   0x08003c24   0x000000d8   Code   RO         1692    i.enet_phy_config   gd32f4xx_enet.o
    0x08003cfc   0x08003cfc   0x0000009c   Code   RO         1693    i.enet_phy_write_read  gd32f4xx_enet.o
    0x08003d98   0x08003d98   0x0000000a   Code   RO         1711    i.enet_rx_desc_immediate_receive_complete_interrupt  gd32f4xx_enet.o
    0x08003da2   0x08003da2   0x00000002   PAD
    0x08003da4   0x08003da4   0x00000024   Code   RO         1713    i.enet_rx_enable    gd32f4xx_enet.o
    0x08003dc8   0x08003dc8   0x000000ac   Code   RO         1714    i.enet_rxframe_drop  gd32f4xx_enet.o
    0x08003e74   0x08003e74   0x00000098   Code   RO         1715    i.enet_rxframe_size_get  gd32f4xx_enet.o
    0x08003f0c   0x08003f0c   0x0000003c   Code   RO         1717    i.enet_software_reset  gd32f4xx_enet.o
    0x08003f48   0x08003f48   0x00000010   Code   RO         1718    i.enet_transmit_checksum_config  gd32f4xx_enet.o
    0x08003f58   0x08003f58   0x00000028   Code   RO         1720    i.enet_tx_enable    gd32f4xx_enet.o
    0x08003f80   0x08003f80   0x00000034   Code   RO         1721    i.enet_txfifo_flush  gd32f4xx_enet.o
    0x08003fb4   0x08003fb4   0x00000070   Code   RO         9494    i.eth_rece_data_task  networkinterface.o
    0x08004024   0x08004024   0x0000002a   Code   RO         9711    i.eth_rmii_gpio_conifg  enet.o
    0x0800404e   0x0800404e   0x00000002   PAD
    0x08004050   0x08004050   0x000000b4   Code   RO         9495    i.ethernet_task_creation  networkinterface.o
    0x08004104   0x08004104   0x00000024   Code   RO         9655    i.fputc             uart0.o
    0x08004128   0x08004128   0x0000005e   Code   RO         2847    i.gpio_af_set       gd32f4xx_gpio.o
    0x08004186   0x08004186   0x00000010   Code   RO         2853    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x08004196   0x08004196   0x0000004e   Code   RO         2855    i.gpio_mode_set     gd32f4xx_gpio.o
    0x080041e4   0x080041e4   0x00000042   Code   RO         2857    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x08004226   0x08004226   0x00000002   PAD
    0x08004228   0x08004228   0x000001dc   Code   RO         9588    i.http_server_task  main.o
    0x08004404   0x08004404   0x000000bc   Code   RO         8166    i.lTCPAddRxdata     freertos_sockets.o
    0x080044c0   0x080044c0   0x0000000e   Code   RO         9110    i.lTCPIncrementTxPosition  freertos_tcp_win.o
    0x080044ce   0x080044ce   0x00000002   PAD
    0x080044d0   0x080044d0   0x00000140   Code   RO         9111    i.lTCPWindowRxCheck  freertos_tcp_win.o
    0x08004610   0x08004610   0x000000fc   Code   RO         9112    i.lTCPWindowTxAdd   freertos_tcp_win.o
    0x0800470c   0x0800470c   0x00000080   Code   RO         9589    i.main              main.o
    0x0800478c   0x0800478c   0x000000c4   Code   RO         3420    i.nvic_irq_enable   gd32f4xx_misc.o
    0x08004850   0x08004850   0x00000014   Code   RO         3421    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x08004864   0x08004864   0x0000012e   Code   RO         7483    i.parseDNSAnswer    freertos_dns_parser.o
    0x08004992   0x08004992   0x00000002   PAD
    0x08004994   0x08004994   0x000000a4   Code   RO         7484    i.prepareReplyDNSMessage  freertos_dns_parser.o
    0x08004a38   0x08004a38   0x000000c0   Code   RO         6388    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x08004af8   0x08004af8   0x000000e4   Code   RO         6389    i.prvAddNewTaskToReadyList  tasks.o
    0x08004bdc   0x08004bdc   0x00000114   Code   RO         7615    i.prvAllowIPPacket  freertos_ip.o
    0x08004cf0   0x08004cf0   0x00000050   Code   RO         7039    i.prvCacheLookup    freertos_arp.o
    0x08004d40   0x08004d40   0x00000064   Code   RO         6722    i.prvCheckForValidListAndQueue  timers.o
    0x08004da4   0x08004da4   0x00000094   Code   RO         8732    i.prvCheckOptions   freertos_tcp_reception.o
    0x08004e38   0x08004e38   0x00000092   Code   RO         8733    i.prvCheckRxData    freertos_tcp_reception.o
    0x08004eca   0x08004eca   0x00000002   PAD
    0x08004ecc   0x08004ecc   0x00000048   Code   RO         6390    i.prvCheckTasksWaitingTermination  tasks.o
    0x08004f14   0x08004f14   0x0000002a   Code   RO         6024    i.prvCopyDataFromQueue  queue.o
    0x08004f3e   0x08004f3e   0x0000006a   Code   RO         6025    i.prvCopyDataToQueue  queue.o
    0x08004fa8   0x08004fa8   0x000000a8   Code   RO         9113    i.prvCreateSectors  freertos_tcp_win.o
    0x08005050   0x08005050   0x00000012   Code   RO         6391    i.prvDeleteTCB      tasks.o
    0x08005062   0x08005062   0x00000046   Code   RO         8167    i.prvDetermineSocketSize  freertos_sockets.o
    0x080050a8   0x080050a8   0x0000004c   Code   RO         7359    i.prvFindEntryIndex  freertos_dns_cache.o
    0x080050f4   0x080050f4   0x00000088   Code   RO         7360    i.prvGetCacheIPEntry  freertos_dns_cache.o
    0x0800517c   0x0800517c   0x00000028   Code   RO         6723    i.prvGetNextExpireTime  timers.o
    0x080051a4   0x080051a4   0x00000074   Code   RO         8169    i.prvGetPrivatePortNumber  freertos_sockets.o
    0x08005218   0x08005218   0x00000298   Code   RO         8809    i.prvHandleEstablished  freertos_tcp_state_handling.o
    0x080054b0   0x080054b0   0x0000000c   Code   RO         7616    i.prvHandleEthernetPacket  freertos_ip.o
    0x080054bc   0x080054bc   0x00000160   Code   RO         8810    i.prvHandleListen   freertos_tcp_state_handling.o
    0x0800561c   0x0800561c   0x00000214   Code   RO         8811    i.prvHandleSynReceived  freertos_tcp_state_handling.o
    0x08005830   0x08005830   0x00000070   Code   RO         6886    i.prvHeapInit       heap_4.o
    0x080058a0   0x080058a0   0x00000044   Code   RO         7617    i.prvIPTask         freertos_ip.o
    0x080058e4   0x080058e4   0x00000048   Code   RO         7844    i.prvIPTimerCheck   freertos_ip_timers.o
    0x0800592c   0x0800592c   0x00000012   Code   RO         7845    i.prvIPTimerReload  freertos_ip_timers.o
    0x0800593e   0x0800593e   0x00000030   Code   RO         7846    i.prvIPTimerStart   freertos_ip_timers.o
    0x0800596e   0x0800596e   0x00000002   PAD
    0x08005970   0x08005970   0x00000028   Code   RO         6392    i.prvIdleTask       tasks.o
    0x08005998   0x08005998   0x0000002a   Code   RO         6026    i.prvInitialiseNewQueue  queue.o
    0x080059c2   0x080059c2   0x00000088   Code   RO         6393    i.prvInitialiseNewTask  tasks.o
    0x08005a4a   0x08005a4a   0x00000002   PAD
    0x08005a4c   0x08005a4c   0x00000068   Code   RO         6394    i.prvInitialiseTaskLists  tasks.o
    0x08005ab4   0x08005ab4   0x00000068   Code   RO         6887    i.prvInsertBlockIntoFreeList  heap_4.o
    0x08005b1c   0x08005b1c   0x0000007c   Code   RO         7361    i.prvInsertCacheEntry  freertos_dns_cache.o
    0x08005b98   0x08005b98   0x00000058   Code   RO         6725    i.prvInsertTimerInActiveList  timers.o
    0x08005bf0   0x08005bf0   0x0000001a   Code   RO         6027    i.prvIsQueueEmpty   queue.o
    0x08005c0a   0x08005c0a   0x0000001e   Code   RO         6028    i.prvIsQueueFull    queue.o
    0x08005c28   0x08005c28   0x00000058   Code   RO         6029    i.prvNotifyQueueSetContainer  queue.o
    0x08005c80   0x08005c80   0x00000022   Code   RO         7966    i.prvPacketBuffer_to_NetworkBuffer  freertos_ip_utils.o
    0x08005ca2   0x08005ca2   0x00000002   PAD
    0x08005ca4   0x08005ca4   0x00000098   Code   RO         7618    i.prvProcessEthernetPacket  freertos_ip.o
    0x08005d3c   0x08005d3c   0x00000044   Code   RO         6726    i.prvProcessExpiredTimer  timers.o
    0x08005d80   0x08005d80   0x00000054   Code   RO         7534    i.prvProcessICMPEchoRequest  freertos_icmp.o
    0x08005dd4   0x08005dd4   0x0000010c   Code   RO         7619    i.prvProcessIPEventsAndTimers  freertos_ip.o
    0x08005ee0   0x08005ee0   0x000001a4   Code   RO         7620    i.prvProcessIPPacket  freertos_ip.o
    0x08006084   0x08006084   0x00000040   Code   RO         7967    i.prvProcessNetworkDownEvent  freertos_ip_utils.o
    0x080060c4   0x080060c4   0x00000110   Code   RO         6727    i.prvProcessReceivedCommands  timers.o
    0x080061d4   0x080061d4   0x00000074   Code   RO         6728    i.prvProcessTimerOrBlockTask  timers.o
    0x08006248   0x08006248   0x0000005e   Code   RO         8734    i.prvReadSackOption  freertos_tcp_reception.o
    0x080062a6   0x080062a6   0x00000028   Code   RO         6729    i.prvReloadTimer    timers.o
    0x080062ce   0x080062ce   0x00000002   PAD
    0x080062d0   0x080062d0   0x00000030   Code   RO         6395    i.prvResetNextTaskUnblockTime  tasks.o
    0x08006300   0x08006300   0x0000002c   Code   RO         6730    i.prvSampleTimeNow  timers.o
    0x0800632c   0x0800632c   0x0000019c   Code   RO         8908    i.prvSendData       freertos_tcp_transmission.o
    0x080064c8   0x080064c8   0x00000140   Code   RO         8909    i.prvSetOptions     freertos_tcp_transmission.o
    0x08006608   0x08006608   0x00000058   Code   RO         8910    i.prvSetSynAckOptions  freertos_tcp_transmission.o
    0x08006660   0x08006660   0x00000178   Code   RO         8735    i.prvSingleStepTCPHeaderOptions  freertos_tcp_reception.o
    0x080067d8   0x080067d8   0x00000084   Code   RO         9048    i.prvSocketSetMSS   freertos_tcp_utils.o
    0x0800685c   0x0800685c   0x0000010c   Code   RO         8736    i.prvStoreRxData    freertos_tcp_reception.o
    0x08006968   0x08006968   0x00000044   Code   RO         6731    i.prvSwitchTimerLists  timers.o
    0x080069ac   0x080069ac   0x00000030   Code   RO         8911    i.prvTCPAddTxData   freertos_tcp_transmission.o
    0x080069dc   0x080069dc   0x00000080   Code   RO         8912    i.prvTCPBufferResize  freertos_tcp_transmission.o
    0x08006a5c   0x08006a5c   0x00000128   Code   RO         8173    i.prvTCPCreateStream  freertos_sockets.o
    0x08006b84   0x08006b84   0x00000094   Code   RO         8913    i.prvTCPCreateWindow  freertos_tcp_transmission.o
    0x08006c18   0x08006c18   0x000000a0   Code   RO         9049    i.prvTCPFlagMeaning  freertos_tcp_utils.o
    0x08006cb8   0x08006cb8   0x00000164   Code   RO         8812    i.prvTCPHandleFin   freertos_tcp_state_handling.o
    0x08006e1c   0x08006e1c   0x000001f8   Code   RO         8813    i.prvTCPHandleState  freertos_tcp_state_handling.o
    0x08007014   0x08007014   0x00000020   Code   RO         8914    i.prvTCPMakeSurePrepared  freertos_tcp_transmission.o
    0x08007034   0x08007034   0x000000fc   Code   RO         8624    i.prvTCPNextTimeout  freertos_tcp_ip.o
    0x08007130   0x08007130   0x000001e0   Code   RO         8915    i.prvTCPPrepareConnect  freertos_tcp_transmission.o
    0x08007310   0x08007310   0x000002f4   Code   RO         8916    i.prvTCPPrepareSend  freertos_tcp_transmission.o
    0x08007604   0x08007604   0x000002f8   Code   RO         8917    i.prvTCPReturnPacket  freertos_tcp_transmission.o
    0x080078fc   0x080078fc   0x0000000e   Code   RO         8918    i.prvTCPSendChallengeAck  freertos_tcp_transmission.o
    0x0800790a   0x0800790a   0x00000070   Code   RO         8174    i.prvTCPSendCheck   freertos_sockets.o
    0x0800797a   0x0800797a   0x00000002   PAD
    0x0800797c   0x0800797c   0x000000b4   Code   RO         8919    i.prvTCPSendPacket  freertos_tcp_transmission.o
    0x08007a30   0x08007a30   0x0000003e   Code   RO         8920    i.prvTCPSendRepeated  freertos_tcp_transmission.o
    0x08007a6e   0x08007a6e   0x0000000e   Code   RO         8921    i.prvTCPSendReset   freertos_tcp_transmission.o
    0x08007a7c   0x08007a7c   0x00000028   Code   RO         8922    i.prvTCPSendSpecialPacketHelper  freertos_tcp_transmission.o
    0x08007aa4   0x08007aa4   0x000000d4   Code   RO         8175    i.prvTCPSetSocketCount  freertos_sockets.o
    0x08007b78   0x08007b78   0x00000114   Code   RO         8814    i.prvTCPSocketCopy  freertos_tcp_state_handling.o
    0x08007c8c   0x08007c8c   0x00000038   Code   RO         8815    i.prvTCPSocketIsActive  freertos_tcp_state_handling.o
    0x08007cc4   0x08007cc4   0x000000c4   Code   RO         8816    i.prvTCPStatusAgeCheck  freertos_tcp_state_handling.o
    0x08007d88   0x08007d88   0x00000028   Code   RO         8625    i.prvTCPTouchSocket  freertos_tcp_ip.o
    0x08007db0   0x08007db0   0x000000d8   Code   RO         9114    i.prvTCPWindowFastRetransmit  freertos_tcp_win.o
    0x08007e88   0x08007e88   0x0000009c   Code   RO         9115    i.prvTCPWindowRx_ExpectedRX  freertos_tcp_win.o
    0x08007f24   0x08007f24   0x00000168   Code   RO         9116    i.prvTCPWindowRx_UnexpectedRX  freertos_tcp_win.o
    0x0800808c   0x0800808c   0x00000070   Code   RO         9117    i.prvTCPWindowTxAdd_FrontSegment  freertos_tcp_win.o
    0x080080fc   0x080080fc   0x00000160   Code   RO         9118    i.prvTCPWindowTxCheckAck  freertos_tcp_win.o
    0x0800825c   0x0800825c   0x0000004a   Code   RO         9119    i.prvTCPWindowTxCheckAck_CalcSRTT  freertos_tcp_win.o
    0x080082a6   0x080082a6   0x0000005e   Code   RO         9120    i.prvTCPWindowTxHasSpace  freertos_tcp_win.o
    0x08008304   0x08008304   0x00000016   Code   RO         6957    i.prvTaskExitError  port.o
    0x0800831a   0x0800831a   0x0000001e   Code   RO         5891    i.prvTestWaitCondition  event_groups.o
    0x08008338   0x08008338   0x0000001a   Code   RO         6732    i.prvTimerTask      timers.o
    0x08008352   0x08008352   0x00000090   Code   RO         6030    i.prvUnlockQueue    queue.o
    0x080083e2   0x080083e2   0x00000002   PAD
    0x080083e4   0x080083e4   0x00000030   Code   RO         7362    i.prvUpdateCacheEntry  freertos_dns_cache.o
    0x08008414   0x08008414   0x0000002a   Code   RO         8176    i.prvValidSocket    freertos_sockets.o
    0x0800843e   0x0800843e   0x00000002   PAD
    0x08008440   0x08008440   0x00000068   Code   RO         8923    i.prvWinScaleFactor  freertos_tcp_transmission.o
    0x080084a8   0x080084a8   0x000000e4   Code   RO         6889    i.pvPortMalloc      heap_4.o
    0x0800858c   0x0800858c   0x0000003c   Code   RO         7968    i.pxDuplicateNetworkBufferWithDescriptor  freertos_ip_utils.o
    0x080085c8   0x080085c8   0x0000009c   Code   RO         9416    i.pxGetNetworkBufferWithDescriptor  bufferallocation_2.o
    0x08008664   0x08008664   0x00000030   Code   RO         8177    i.pxListFindListItemWithValue  freertos_sockets.o
    0x08008694   0x08008694   0x0000002c   Code   RO         6958    i.pxPortInitialiseStack  port.o
    0x080086c0   0x080086c0   0x0000005c   Code   RO         8178    i.pxTCPSocketLookup  freertos_sockets.o
    0x0800871c   0x0800871c   0x0000008c   Code   RO         9121    i.pxTCPWindowTx_GetTXQueue  freertos_tcp_win.o
    0x080087a8   0x080087a8   0x00000078   Code   RO         9122    i.pxTCPWindowTx_GetWaitQueue  freertos_tcp_win.o
    0x08008820   0x08008820   0x0000000e   Code   RO         7969    i.pxUDPPayloadBuffer_to_NetworkBuffer  freertos_ip_utils.o
    0x0800882e   0x0800882e   0x00000002   PAD
    0x08008830   0x08008830   0x0000001c   Code   RO         8179    i.pxUDPSocketLookup  freertos_sockets.o
    0x0800884c   0x0800884c   0x0000001c   Code   RO         3623    i.rcu_ckout0_config  gd32f4xx_rcu.o
    0x08008868   0x08008868   0x00000124   Code   RO         3625    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x0800898c   0x0800898c   0x00000024   Code   RO         3644    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x080089b0   0x080089b0   0x00000024   Code   RO         3647    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x080089d4   0x080089d4   0x00000024   Code   RO         3648    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x080089f8   0x080089f8   0x00000134   Code   RO         9590    i.start_task        main.o
    0x08008b2c   0x08008b2c   0x00000018   Code   RO         4719    i.syscfg_enet_phy_interface_config  gd32f4xx_syscfg.o
    0x08008b44   0x08008b44   0x000000fc   Code   RO          297    i.system_clock_200m_25m_hxtal  system_gd32f4xx.o
    0x08008c40   0x08008c40   0x00000008   Code   RO          298    i.system_clock_config  system_gd32f4xx.o
    0x08008c48   0x08008c48   0x000001a0   Code   RO         9591    i.tcp_server_task   main.o
    0x08008de8   0x08008de8   0x0000001e   Code   RO         9751    i.trng_config       trng.o
    0x08008e06   0x08008e06   0x00000014   Code   RO         5394    i.trng_deinit       gd32f4xx_trng.o
    0x08008e1a   0x08008e1a   0x00000002   PAD
    0x08008e1c   0x08008e1c   0x00000014   Code   RO         5396    i.trng_enable       gd32f4xx_trng.o
    0x08008e30   0x08008e30   0x00000018   Code   RO         5397    i.trng_flag_get     gd32f4xx_trng.o
    0x08008e48   0x08008e48   0x0000000c   Code   RO         5398    i.trng_get_true_random_data  gd32f4xx_trng.o
    0x08008e54   0x08008e54   0x0000004c   Code   RO         9752    i.trng_init         trng.o
    0x08008ea0   0x08008ea0   0x0000001e   Code   RO         9753    i.trng_random_range_get  trng.o
    0x08008ebe   0x08008ebe   0x00000002   PAD
    0x08008ec0   0x08008ec0   0x000000b4   Code   RO         9754    i.trng_ready_check  trng.o
    0x08008f74   0x08008f74   0x0000008c   Code   RO         9656    i.uart0_init        uart0.o
    0x08009000   0x08009000   0x00000014   Code   RO         9496    i.ulApplicationGetNextSequenceNumber  networkinterface.o
    0x08009014   0x08009014   0x00000018   Code   RO         7970    i.ulChar2u32        freertos_ip_utils.o
    0x0800902c   0x0800902c   0x00000028   Code   RO         7242    i.ulDNSHandlePacket  freertos_dns.o
    0x08009054   0x08009054   0x00000028   Code   RO         7243    i.ulNBNSHandlePacket  freertos_dns.o
    0x0800907c   0x0800907c   0x00000034   Code   RO         9123    i.ulTCPWindowTxAck  freertos_tcp_win.o
    0x080090b0   0x080090b0   0x00000110   Code   RO         9124    i.ulTCPWindowTxGet  freertos_tcp_win.o
    0x080091c0   0x080091c0   0x00000094   Code   RO         9125    i.ulTCPWindowTxSack  freertos_tcp_win.o
    0x08009254   0x08009254   0x00000088   Code   RO         6398    i.ulTaskGenericNotifyTake  tasks.o
    0x080092dc   0x080092dc   0x00000012   Code   RO         9126    i.ulTimerGetAge     freertos_tcp_win.o
    0x080092ee   0x080092ee   0x0000000c   Code   RO         7971    i.usChar2u16        freertos_ip_utils.o
    0x080092fa   0x080092fa   0x0000018a   Code   RO         7972    i.usGenerateChecksum  freertos_ip_utils.o
    0x08009484   0x08009484   0x000002d0   Code   RO         7973    i.usGenerateProtocolChecksum  freertos_ip_utils.o
    0x08009754   0x08009754   0x000000e8   Code   RO         5463    i.usart_baudrate_set  gd32f4xx_usart.o
    0x0800983c   0x0800983c   0x00000008   Code   RO         5468    i.usart_data_transmit  gd32f4xx_usart.o
    0x08009844   0x08009844   0x000000dc   Code   RO         5469    i.usart_deinit      gd32f4xx_usart.o
    0x08009920   0x08009920   0x0000000a   Code   RO         5473    i.usart_enable      gd32f4xx_usart.o
    0x0800992a   0x0800992a   0x0000001e   Code   RO         5475    i.usart_flag_get    gd32f4xx_usart.o
    0x08009948   0x08009948   0x00000010   Code   RO         5515    i.usart_transmit_config  gd32f4xx_usart.o
    0x08009958   0x08009958   0x0000000c   Code   RO         9419    i.uxGetNumberOfFreeNetworkBuffers  bufferallocation_2.o
    0x08009964   0x08009964   0x00000028   Code   RO         5979    i.uxListRemove      list.o
    0x0800998c   0x0800998c   0x00000012   Code   RO         6031    i.uxQueueMessagesWaiting  queue.o
    0x0800999e   0x0800999e   0x00000008   Code   RO         9497    i.uxRand            networkinterface.o
    0x080099a6   0x080099a6   0x000000a2   Code   RO         8534    i.uxStreamBufferAdd  freertos_stream_buffer.o
    0x08009a48   0x08009a48   0x00000016   Code   RO         8535    i.uxStreamBufferDistance  freertos_stream_buffer.o
    0x08009a5e   0x08009a5e   0x00000014   Code   RO         8536    i.uxStreamBufferFrontSpace  freertos_stream_buffer.o
    0x08009a72   0x08009a72   0x0000008c   Code   RO         8537    i.uxStreamBufferGet  freertos_stream_buffer.o
    0x08009afe   0x08009afe   0x00000028   Code   RO         8538    i.uxStreamBufferGetPtr  freertos_stream_buffer.o
    0x08009b26   0x08009b26   0x00000014   Code   RO         8539    i.uxStreamBufferGetSize  freertos_stream_buffer.o
    0x08009b3a   0x08009b3a   0x00000014   Code   RO         8540    i.uxStreamBufferGetSpace  freertos_stream_buffer.o
    0x08009b4e   0x08009b4e   0x00000014   Code   RO         8541    i.uxStreamBufferMidSpace  freertos_stream_buffer.o
    0x08009b62   0x08009b62   0x00000018   Code   RO         8542    i.uxStreamBufferSpace  freertos_stream_buffer.o
    0x08009b7a   0x08009b7a   0x00000002   PAD
    0x08009b7c   0x08009b7c   0x0000000c   Code   RO         6400    i.uxTaskGetNumberOfTasks  tasks.o
    0x08009b88   0x08009b88   0x0000001c   Code   RO         6404    i.uxTaskResetEventItemValue  tasks.o
    0x08009ba4   0x08009ba4   0x000000bc   Code   RO         7040    i.vARPAgeCache      freertos_arp.o
    0x08009c60   0x08009c60   0x0000004c   Code   RO         7041    i.vARPGenerateRequestPacket  freertos_arp.o
    0x08009cac   0x08009cac   0x00000164   Code   RO         7042    i.vARPRefreshCacheEntry  freertos_arp.o
    0x08009e10   0x08009e10   0x00000014   Code   RO         7847    i.vARPTimerReload   freertos_ip_timers.o
    0x08009e24   0x08009e24   0x000001f0   Code   RO         9498    i.vApplicationIPNetworkEventHook  networkinterface.o
    0x0800a014   0x0800a014   0x00000094   Code   RO         7848    i.vCheckNetworkTimers  freertos_ip_timers.o
    0x0800a0a8   0x0800a0a8   0x0000002a   Code   RO         5893    i.vEventGroupDelete  event_groups.o
    0x0800a0d2   0x0800a0d2   0x00000002   PAD
    0x0800a0d4   0x0800a0d4   0x0000001c   Code   RO         7621    i.vIPNetworkUpCalls  freertos_ip.o
    0x0800a0f0   0x0800a0f0   0x00000024   Code   RO         7849    i.vIPSetARPResolutionTimerEnableState  freertos_ip_timers.o
    0x0800a114   0x0800a114   0x00000024   Code   RO         7850    i.vIPSetARPTimerEnableState  freertos_ip_timers.o
    0x0800a138   0x0800a138   0x00000030   Code   RO         7851    i.vIPSetTCPTimerExpiredState  freertos_ip_timers.o
    0x0800a168   0x0800a168   0x00000014   Code   RO         7852    i.vIPTimerStartARPResolution  freertos_ip_timers.o
    0x0800a17c   0x0800a17c   0x0000001a   Code   RO         5980    i.vListInitialise   list.o
    0x0800a196   0x0800a196   0x00000006   Code   RO         5981    i.vListInitialiseItem  list.o
    0x0800a19c   0x0800a19c   0x00000034   Code   RO         5982    i.vListInsert       list.o
    0x0800a1d0   0x0800a1d0   0x00000018   Code   RO         5983    i.vListInsertEnd    list.o
    0x0800a1e8   0x0800a1e8   0x00000014   Code   RO         9127    i.vListInsertFifo   freertos_tcp_win.o
    0x0800a1fc   0x0800a1fc   0x00000016   Code   RO         9128    i.vListInsertGeneric  freertos_tcp_win.o
    0x0800a212   0x0800a212   0x00000002   Code   RO         9499    i.vLoggingPrintf    networkinterface.o
    0x0800a214   0x0800a214   0x00000018   Code   RO         8181    i.vNetworkSocketsInit  freertos_sockets.o
    0x0800a22c   0x0800a22c   0x00000024   Code   RO         6960    i.vPortEnterCritical  port.o
    0x0800a250   0x0800a250   0x00000020   Code   RO         6961    i.vPortExitCritical  port.o
    0x0800a270   0x0800a270   0x0000004c   Code   RO         6890    i.vPortFree         heap_4.o
    0x0800a2bc   0x0800a2bc   0x00000028   Code   RO         6962    i.vPortSetupTimerInterrupt  port.o
    0x0800a2e4   0x0800a2e4   0x00000002   Code   RO         7974    i.vPreCheckConfigs  freertos_ip_utils.o
    0x0800a2e6   0x0800a2e6   0x00000002   PAD
    0x0800a2e8   0x0800a2e8   0x00000084   Code   RO         7044    i.vProcessARPPacketReply  freertos_arp.o
    0x0800a36c   0x0800a36c   0x000000fc   Code   RO         9352    i.vProcessGeneratedUDPPacket  freertos_udp_ip.o
    0x0800a468   0x0800a468   0x00000048   Code   RO         6034    i.vQueueAddToRegistry  queue.o
    0x0800a4b0   0x0800a4b0   0x00000014   Code   RO         6035    i.vQueueDelete      queue.o
    0x0800a4c4   0x0800a4c4   0x00000030   Code   RO         6036    i.vQueueUnregisterQueue  queue.o
    0x0800a4f4   0x0800a4f4   0x0000004a   Code   RO         6037    i.vQueueWaitForMessageRestricted  queue.o
    0x0800a53e   0x0800a53e   0x00000012   Code   RO         9420    i.vReleaseNetworkBuffer  bufferallocation_2.o
    0x0800a550   0x0800a550   0x0000004c   Code   RO         9421    i.vReleaseNetworkBufferAndDescriptor  bufferallocation_2.o
    0x0800a59c   0x0800a59c   0x00000034   Code   RO         7622    i.vReturnEthernetFrame  freertos_ip.o
    0x0800a5d0   0x0800a5d0   0x00000032   Code   RO         7975    i.vSetMultiCastIPv4MacAddress  freertos_ip_utils.o
    0x0800a602   0x0800a602   0x00000002   PAD
    0x0800a604   0x0800a604   0x000000dc   Code   RO         8182    i.vSocketBind       freertos_sockets.o
    0x0800a6e0   0x0800a6e0   0x000000dc   Code   RO         8183    i.vSocketClose      freertos_sockets.o
    0x0800a7bc   0x0800a7bc   0x00000024   Code   RO         8626    i.vSocketCloseNextTime  freertos_tcp_ip.o
    0x0800a7e0   0x0800a7e0   0x0000002c   Code   RO         8627    i.vSocketListenNextTime  freertos_tcp_ip.o
    0x0800a80c   0x0800a80c   0x0000018c   Code   RO         8184    i.vSocketSelect     freertos_sockets.o
    0x0800a998   0x0800a998   0x00000040   Code   RO         8185    i.vSocketWakeUpUser  freertos_sockets.o
    0x0800a9d8   0x0800a9d8   0x0000000c   Code   RO         8543    i.vStreamBufferClear  freertos_stream_buffer.o
    0x0800a9e4   0x0800a9e4   0x0000002c   Code   RO         8544    i.vStreamBufferMoveMid  freertos_stream_buffer.o
    0x0800aa10   0x0800aa10   0x0000023c   Code   RO         8628    i.vTCPStateChange   freertos_tcp_ip.o
    0x0800ac4c   0x0800ac4c   0x00000014   Code   RO         7853    i.vTCPTimerReload   freertos_ip_timers.o
    0x0800ac60   0x0800ac60   0x0000000c   Code   RO         9130    i.vTCPTimerSet      freertos_tcp_win.o
    0x0800ac6c   0x0800ac6c   0x000000a0   Code   RO         9131    i.vTCPWindowCreate  freertos_tcp_win.o
    0x0800ad0c   0x0800ad0c   0x00000038   Code   RO         9132    i.vTCPWindowDestroy  freertos_tcp_win.o
    0x0800ad44   0x0800ad44   0x00000034   Code   RO         9133    i.vTCPWindowFree    freertos_tcp_win.o
    0x0800ad78   0x0800ad78   0x00000054   Code   RO         9134    i.vTCPWindowInit    freertos_tcp_win.o
    0x0800adcc   0x0800adcc   0x00000034   Code   RO         6405    i.vTaskDelay        tasks.o
    0x0800ae00   0x0800ae00   0x000000d4   Code   RO         6406    i.vTaskDelete       tasks.o
    0x0800aed4   0x0800aed4   0x00000150   Code   RO         6408    i.vTaskGenericNotifyGiveFromISR  tasks.o
    0x0800b024   0x0800b024   0x00000018   Code   RO         6409    i.vTaskInternalSetTimeOutState  tasks.o
    0x0800b03c   0x0800b03c   0x0000000c   Code   RO         6410    i.vTaskMissedYield  tasks.o
    0x0800b048   0x0800b048   0x00000020   Code   RO         6411    i.vTaskPlaceOnEventList  tasks.o
    0x0800b068   0x0800b068   0x0000004c   Code   RO         6412    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x0800b0b4   0x0800b0b4   0x00000050   Code   RO         6413    i.vTaskPlaceOnUnorderedEventList  tasks.o
    0x0800b104   0x0800b104   0x000000d0   Code   RO         6415    i.vTaskRemoveFromUnorderedEventList  tasks.o
    0x0800b1d4   0x0800b1d4   0x00000024   Code   RO         6417    i.vTaskSetTimeOutState  tasks.o
    0x0800b1f8   0x0800b1f8   0x00000078   Code   RO         6418    i.vTaskStartScheduler  tasks.o
    0x0800b270   0x0800b270   0x00000010   Code   RO         6420    i.vTaskSuspendAll   tasks.o
    0x0800b280   0x0800b280   0x00000060   Code   RO         6421    i.vTaskSwitchContext  tasks.o
    0x0800b2e0   0x0800b2e0   0x00000006   Code   RO         9500    i.xApplicationDNSQueryHook  networkinterface.o
    0x0800b2e6   0x0800b2e6   0x0000000e   Code   RO         9501    i.xApplicationGetRandomNumber  networkinterface.o
    0x0800b2f4   0x0800b2f4   0x0000003c   Code   RO         7854    i.xCalculateSleepTime  freertos_ip_timers.o
    0x0800b330   0x0800b330   0x00000044   Code   RO         7047    i.xCheckRequiresARPResolution  freertos_arp.o
    0x0800b374   0x0800b374   0x000000a4   Code   RO         7623    i.xCheckSizeFields  freertos_ip.o
    0x0800b418   0x0800b418   0x00000020   Code   RO         5895    i.xEventGroupClearBits  event_groups.o
    0x0800b438   0x0800b438   0x0000001a   Code   RO         5896    i.xEventGroupCreate  event_groups.o
    0x0800b452   0x0800b452   0x00000092   Code   RO         5898    i.xEventGroupSetBits  event_groups.o
    0x0800b4e4   0x0800b4e4   0x000000cc   Code   RO         5900    i.xEventGroupWaitBits  event_groups.o
    0x0800b5b0   0x0800b5b0   0x0000000c   Code   RO         7624    i.xIPIsNetworkTaskReady  freertos_ip.o
    0x0800b5bc   0x0800b5bc   0x0000001c   Code   RO         7976    i.xIsCallingFromIPTask  freertos_ip_utils.o
    0x0800b5d8   0x0800b5d8   0x00000038   Code   RO         7048    i.xIsIPInARPCache   freertos_arp.o
    0x0800b610   0x0800b610   0x0000002e   Code   RO         7625    i.xIsIPv4Multicast  freertos_ip.o
    0x0800b63e   0x0800b63e   0x00000002   PAD
    0x0800b640   0x0800b640   0x000000a8   Code   RO         9422    i.xNetworkBuffersInitialise  bufferallocation_2.o
    0x0800b6e8   0x0800b6e8   0x00000028   Code   RO         9502    i.xNetworkInterfaceInitialise  networkinterface.o
    0x0800b710   0x0800b710   0x0000001a   Code   RO         9503    i.xNetworkInterfaceOutput  networkinterface.o
    0x0800b72a   0x0800b72a   0x00000002   PAD
    0x0800b72c   0x0800b72c   0x00000048   Code   RO         6963    i.xPortStartScheduler  port.o
    0x0800b774   0x0800b774   0x0000033c   Code   RO         8629    i.xProcessReceivedTCPPacket  freertos_tcp_ip.o
    0x0800bab0   0x0800bab0   0x000000e4   Code   RO         9353    i.xProcessReceivedUDPPacket  freertos_udp_ip.o
    0x0800bb94   0x0800bb94   0x00000022   Code   RO         6039    i.xQueueCreateCountingSemaphore  queue.o
    0x0800bbb6   0x0800bbb6   0x0000004c   Code   RO         6041    i.xQueueGenericCreate  queue.o
    0x0800bc02   0x0800bc02   0x00000002   PAD
    0x0800bc04   0x0800bc04   0x000000a0   Code   RO         6042    i.xQueueGenericReset  queue.o
    0x0800bca4   0x0800bca4   0x00000148   Code   RO         6043    i.xQueueGenericSend  queue.o
    0x0800bdec   0x0800bdec   0x00000104   Code   RO         6050    i.xQueueReceive     queue.o
    0x0800bef0   0x0800bef0   0x000000f0   Code   RO         6055    i.xQueueSemaphoreTake  queue.o
    0x0800bfe0   0x0800bfe0   0x0000009c   Code   RO         7627    i.xSendEventStructToIPTask  freertos_ip.o
    0x0800c07c   0x0800c07c   0x00000018   Code   RO         7628    i.xSendEventToIPTask  freertos_ip.o
    0x0800c094   0x0800c094   0x00000012   Code   RO         9135    i.xSequenceGreaterThan  freertos_tcp_win.o
    0x0800c0a6   0x0800c0a6   0x00000010   Code   RO         9136    i.xSequenceGreaterThanOrEqual  freertos_tcp_win.o
    0x0800c0b6   0x0800c0b6   0x00000012   Code   RO         9137    i.xSequenceLessThan  freertos_tcp_win.o
    0x0800c0c8   0x0800c0c8   0x00000014   Code   RO         8545    i.xStreamBufferLessThenEqual  freertos_stream_buffer.o
    0x0800c0dc   0x0800c0dc   0x0000008c   Code   RO         8630    i.xTCPCheckNewClient  freertos_tcp_ip.o
    0x0800c168   0x0800c168   0x000000e4   Code   RO         8631    i.xTCPSocketCheck   freertos_tcp_ip.o
    0x0800c24c   0x0800c24c   0x000000a4   Code   RO         8187    i.xTCPTimerCheck    freertos_sockets.o
    0x0800c2f0   0x0800c2f0   0x00000022   Code   RO         9138    i.xTCPWindowGetHead  freertos_tcp_win.o
    0x0800c312   0x0800c312   0x00000002   PAD
    0x0800c314   0x0800c314   0x000000cc   Code   RO         9139    i.xTCPWindowNew     freertos_tcp_win.o
    0x0800c3e0   0x0800c3e0   0x00000018   Code   RO         9140    i.xTCPWindowPeekHead  freertos_tcp_win.o
    0x0800c3f8   0x0800c3f8   0x000000d8   Code   RO         9141    i.xTCPWindowRxConfirm  freertos_tcp_win.o
    0x0800c4d0   0x0800c4d0   0x0000006c   Code   RO         9142    i.xTCPWindowRxEmpty  freertos_tcp_win.o
    0x0800c53c   0x0800c53c   0x00000026   Code   RO         9143    i.xTCPWindowRxFind  freertos_tcp_win.o
    0x0800c562   0x0800c562   0x00000010   Code   RO         9144    i.xTCPWindowTxDone  freertos_tcp_win.o
    0x0800c572   0x0800c572   0x00000090   Code   RO         9145    i.xTCPWindowTxHasData  freertos_tcp_win.o
    0x0800c602   0x0800c602   0x00000002   PAD
    0x0800c604   0x0800c604   0x00000060   Code   RO         6423    i.xTaskCheckForTimeOut  tasks.o
    0x0800c664   0x0800c664   0x00000068   Code   RO         6424    i.xTaskCreate       tasks.o
    0x0800c6cc   0x0800c6cc   0x0000000c   Code   RO         6430    i.xTaskGetCurrentTaskHandle  tasks.o
    0x0800c6d8   0x0800c6d8   0x0000000c   Code   RO         6432    i.xTaskGetTickCount  tasks.o
    0x0800c6e4   0x0800c6e4   0x00000190   Code   RO         6434    i.xTaskIncrementTick  tasks.o
    0x0800c874   0x0800c874   0x00000108   Code   RO         6435    i.xTaskRemoveFromEventList  tasks.o
    0x0800c97c   0x0800c97c   0x0000015c   Code   RO         6436    i.xTaskResumeAll    tasks.o
    0x0800cad8   0x0800cad8   0x00000040   Code   RO         6738    i.xTimerCreateTimerTask  timers.o
    0x0800cb18   0x0800cb18   0x0000000a   Code   RO         9989    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800cb22   0x0800cb22   0x00000026   Data   RO         7050    .constdata          freertos_arp.o
    0x0800cb48   0x0800cb48   0x00000012   Data   RO         7244    .constdata          freertos_dns.o
    0x0800cb5a   0x0800cb5a   0x00000002   PAD
    0x0800cb5c   0x0800cb5c   0x00000018   Data   RO         7630    .constdata          freertos_ip.o
    0x0800cb74   0x0800cb74   0x00000028   Data   RO         8189    .constdata          freertos_sockets.o
    0x0800cb9c   0x0800cb9c   0x00000034   Data   RO         8817    .constdata          freertos_tcp_state_handling.o
    0x0800cbd0   0x0800cbd0   0x00000004   Data   RO         9424    .constdata          bufferallocation_2.o
    0x0800cbd4   0x0800cbd4   0x00000028   Data   RO         9814    .constdata          c_w.l(_printf_hex_int.o)
    0x0800cbfc   0x0800cbfc   0x00000011   Data   RO         9849    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800cc0d   0x0800cc0d   0x00000003   PAD
    0x0800cc10   0x0800cc10   0x0000004c   Data   RO         7977    .conststring        freertos_ip_utils.o
    0x0800cc5c   0x0800cc5c   0x00000047   Data   RO         8190    .conststring        freertos_sockets.o
    0x0800cca3   0x0800cca3   0x00000001   PAD
    0x0800cca4   0x0800cca4   0x000000a3   Data   RO         8818    .conststring        freertos_tcp_state_handling.o
    0x0800cd47   0x0800cd47   0x00000001   PAD
    0x0800cd48   0x0800cd48   0x000001fc   Data   RO         9147    .conststring        freertos_tcp_win.o
    0x0800cf44   0x0800cf44   0x000001a1   Data   RO         9592    .conststring        main.o
    0x0800d0e5   0x0800d0e5   0x00000003   PAD
    0x0800d0e8   0x0800d0e8   0x00000020   Data   RO        10053    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800d108, Size: 0x000233e0, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800d108   0x00000004   Data   RW          299    .data               system_gd32f4xx.o
    0x20000004   0x0800d10c   0x00000014   Data   RW         1728    .data               gd32f4xx_enet.o
    0x20000018   0x0800d120   0x00000040   Data   RW         6439    .data               tasks.o
    0x20000058   0x0800d160   0x00000014   Data   RW         6748    .data               timers.o
    0x2000006c   0x0800d174   0x0000001c   Data   RW         6896    .data               heap_4.o
    0x20000088   0x0800d190   0x00000004   Data   RW         6964    .data               port.o
    0x2000008c   0x0800d194   0x00000014   Data   RW         7051    .data               freertos_arp.o
    0x200000a0   0x0800d1a8   0x00000004   Data   RW         7364    .data               freertos_dns_cache.o
    0x200000a4   0x0800d1ac   0x00000020   Data   RW         7631    .data               freertos_ip.o
    0x200000c4   0x0800d1cc   0x00000004   Data   RW         7978    .data               freertos_ip_utils.o
    0x200000c8   0x0800d1d0   0x00000004   Data   RW         8191    .data               freertos_sockets.o
    0x200000cc   0x0800d1d4   0x00000008   Data   RW         8632    .data               freertos_tcp_ip.o
    0x200000d4   0x0800d1dc   0x0000000c   Data   RW         9148    .data               freertos_tcp_win.o
    0x200000e0   0x0800d1e8   0x00000018   Data   RW         9354    .data               freertos_udp_ip.o
    0x200000f8   0x0800d200   0x00000008   Data   RW         9425    .data               bufferallocation_2.o
    0x20000100   0x0800d208   0x0000000c   Data   RW         9504    .data               networkinterface.o
    0x2000010c   0x0800d214   0x00000016   Data   RW         9593    .data               main.o
    0x20000122   0x0800d22a   0x00000002   PAD
    0x20000124   0x0800d22c   0x00000004   Data   RW         9658    .data               uart0.o
    0x20000128        -       0x00003c64   Zero   RW         1726    .bss                gd32f4xx_enet.o
    0x20003d8c        -       0x00000100   Zero   RW         6056    .bss                queue.o
    0x20003e8c        -       0x000002e4   Zero   RW         6438    .bss                tasks.o
    0x20004170        -       0x00000028   Zero   RW         6747    .bss                timers.o
    0x20004198        -       0x0001e000   Zero   RW         6895    .bss                heap_4.o
    0x20022198        -       0x00000048   Zero   RW         7049    .bss                freertos_arp.o
    0x200221e0        -       0x00000070   Zero   RW         7363    .bss                freertos_dns_cache.o
    0x20022250        -       0x00000028   Zero   RW         7629    .bss                freertos_ip.o
    0x20022278        -       0x0000003c   Zero   RW         7855    .bss                freertos_ip_timers.o
    0x200222b4        -       0x00000028   Zero   RW         8188    .bss                freertos_sockets.o
    0x200222dc        -       0x0000000a   Zero   RW         9050    .bss                freertos_tcp_utils.o
    0x200222e6   0x0800d230   0x00000002   PAD
    0x200222e8        -       0x00000014   Zero   RW         9146    .bss                freertos_tcp_win.o
    0x200222fc        -       0x00000884   Zero   RW         9423    .bss                bufferallocation_2.o
    0x20022b80        -       0x00000060   Zero   RW         9931    .bss                c_w.l(libspace.o)
    0x20022be0        -       0x00000400   Zero   RW          287    HEAP                startup_gd32f450_470.o
    0x20022fe0        -       0x00000400   Zero   RW          286    STACK               startup_gd32f450_470.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       430         58          4          8       2180       5458   bufferallocation_2.o
         0          0          0          0          0        516   croutine.o
      4178       2530          0          0          0       5188   enet.o
       480          6          0          0          0      19138   event_groups.o
      1564        104         38         20         72      27898   freertos_arp.o
         0          0          0          0          0      17915   freertos_dhcp.o
        80          0         18          0          0      22935   freertos_dns.o
       652         88          0          4        112       5925   freertos_dns_cache.o
      1936         30          0          0          0       9254   freertos_dns_parser.o
       132          4          0          0          0      19115   freertos_icmp.o
      2302        350         24         32         40      33240   freertos_ip.o
       526         66          0          0         60      13377   freertos_ip_timers.o
      1478         88         76          4          0      30734   freertos_ip_utils.o
      4212        494        111          4         40      43577   freertos_sockets.o
       544          0          0          0          0       9864   freertos_stream_buffer.o
      2140        464          0          8          0      24779   freertos_tcp_ip.o
      1032         92          0          0          0      23191   freertos_tcp_reception.o
      2964        654        215          0          0      27130   freertos_tcp_state_handling.o
      3586        586          0          0          0      33975   freertos_tcp_transmission.o
       292         72          0          0         10      19073   freertos_tcp_utils.o
      4110        890        508         12         20      29157   freertos_tcp_win.o
       480         16          0         24          0      20818   freertos_udp_ip.o
         0          0          0          0          0       4976   gd32f4xx_adc.o
      2750        256          0         20      15460      16056   gd32f4xx_enet.o
       254          0          0          0          0       2989   gd32f4xx_gpio.o
       160        110          0          0          0     115837   gd32f4xx_it.o
       216         20          0          0          0       1408   gd32f4xx_misc.o
       428         46          0          0          0       4076   gd32f4xx_rcu.o
        24          6          0          0          0        607   gd32f4xx_syscfg.o
        76         18          0          0          0       2124   gd32f4xx_trng.o
       516         18          0          0          0       3922   gd32f4xx_usart.o
       520         58          0         28     122880       5119   heap_4.o
       148          0          0          0          0       3682   list.o
      1968       1100        417         22          0       5405   main.o
      1020        456          0         12          0       9099   networkinterface.o
       488         56          0          4          0      10548   port.o
      1808         30          0          0        256      19473   queue.o
        64         26        428          0       2048        892   startup_gd32f450_470.o
       472         30          0          4          0       2735   system_gd32f4xx.o
      3550        448          0         64        740      39254   tasks.o
       926        110          0         20         40      11792   timers.o
       316        144          0          0          0       2195   trng.o
       180         14          0          4          0       3471   uart0.o

    ----------------------------------------------------------------------
     49058       <USER>       <GROUP>        296     143960     707917   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        56          0          7          2          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_u.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        16          0          0          0          0         68   aeabi_memset.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        88          0          0          0          0         76   memcmp.o
        24          4          0          0          0         84   noretval__2printf.o
        52          4          0          0          0         84   noretval__2snprintf.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
       132          0          0          0          0         68   rt_memmove_v6.o
       122          0          0          0          0         80   rt_memmove_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        72          0          0          0          0         80   strcpy.o
        62          0          0          0          0         76   strlen.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
      2516         <USER>         <GROUP>          0         96       2784   Library Totals
        12          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2494         54         57          0         96       2668   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
      2516         <USER>         <GROUP>          0         96       2784   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     51574       9592       1938        296     144056     674193   Grand Totals
     51574       9592       1938        296     144056     674193   ELF Image Totals
     51574       9592       1938        296          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                53512 (  52.26kB)
    Total RW  Size (RW Data + ZI Data)            144352 ( 140.97kB)
    Total ROM Size (Code + RO Data + RW Data)      53808 (  52.55kB)

==============================================================================

