Dependencies for Project 'project', Target 'Target 1': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (.\CMSIS\gd32f4xx_it.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (CMSIS\gd32f4xx_it.h)(0x688C1A0D)
I (CMSIS\gd32f4xx.h)(0x688C1DB4)
I (CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\user\main.h)(0x688C29EC)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\user\uart0.h)(0x688C1A12)
F (.\CMSIS\startup_gd32f450_470.s)(0x688C1A0D)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

--pd "__UVISION_VERSION SETA 533" --pd "GD32F470 SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (.\CMSIS\system_gd32f4xx.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (CMSIS\gd32f4xx.h)(0x688C1DB4)
I (CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_adc.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_can.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_crc.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_ctc.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_dac.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_dbg.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_dci.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_dma.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_enet.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\user\main.h)(0x688C29EC)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\user\uart0.h)(0x688C1A12)
F (.\FWLib\Source\gd32f4xx_exmc.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_exti.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_fmc.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_fwdgt.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_gpio.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_i2c.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_ipa.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_iref.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_misc.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_pmu.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_rcu.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_rtc.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_sdio.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_spi.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_syscfg.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_timer.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_tli.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_trng.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_usart.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\FWLib\Source\gd32f4xx_wwdgt.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_Kernel\croutine.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\croutine.o --omf_browse .\objects\croutine.crf --depend .\objects\croutine.d)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\croutine.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_Kernel\event_groups.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\event_groups.o --omf_browse .\objects\event_groups.crf --depend .\objects\event_groups.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_Kernel\list.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\list.o --omf_browse .\objects\list.crf --depend .\objects\list.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_Kernel\queue.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\queue.o --omf_browse .\objects\queue.crf --depend .\objects\queue.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_Kernel\stream_buffer.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\stream_buffer.o --omf_browse .\objects\stream_buffer.crf --depend .\objects\stream_buffer.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\stream_buffer.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_Kernel\tasks.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\tasks.o --omf_browse .\objects\tasks.crf --depend .\objects\tasks.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\stack_macros.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_Kernel\timers.c)(0x688C1A0D)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\timers.o --omf_browse .\objects\timers.crf --depend .\objects\timers.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_Kernel\portable\MemMang\heap_4.c)(0x688C1A11)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\heap_4.o --omf_browse .\objects\heap_4.crf --depend .\objects\heap_4.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\port.c)(0x688C1A11)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\port.o --omf_browse .\objects\port.crf --depend .\objects\port.d)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_ARP.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_arp.o --omf_browse .\objects\freertos_arp.crf --depend .\objects\freertos_arp.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Timers.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_DHCP.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_dhcp.o --omf_browse .\objects\freertos_dhcp.crf --depend .\objects\freertos_dhcp.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Timers.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_dns.o --omf_browse .\objects\freertos_dns.crf --depend .\objects\freertos_dns.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Parser.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Networking.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Cache.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_dns_cache.o --omf_browse .\objects\freertos_dns_cache.crf --depend .\objects\freertos_dns_cache.d)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Callback.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_dns_callback.o --omf_browse .\objects\freertos_dns_callback.crf --depend .\objects\freertos_dns_callback.d)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Timers.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Networking.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_dns_networking.o --omf_browse .\objects\freertos_dns_networking.crf --depend .\objects\freertos_dns_networking.d)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Networking.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_DNS_Parser.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_dns_parser.o --omf_browse .\objects\freertos_dns_parser.crf --depend .\objects\freertos_dns_parser.d)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Parser.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_ICMP.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_icmp.o --omf_browse .\objects\freertos_icmp.crf --depend .\objects\freertos_icmp.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ICMP.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_IP.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_ip.o --omf_browse .\objects\freertos_ip.crf --depend .\objects\freertos_ip.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ICMP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Timers.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_IP_Timers.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_ip_timers.o --omf_browse .\objects\freertos_ip_timers.crf --depend .\objects\freertos_ip_timers.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Timers.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_IP_Utils.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_ip_utils.o --omf_browse .\objects\freertos_ip_utils.crf --depend .\objects\freertos_ip_utils.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Timers.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_Sockets.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_sockets.o --omf_browse .\objects\freertos_sockets.crf --depend .\objects\freertos_sockets.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_Stream_Buffer.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_stream_buffer.o --omf_browse .\objects\freertos_stream_buffer.crf --depend .\objects\freertos_stream_buffer.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_IP.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_tcp_ip.o --omf_browse .\objects\freertos_tcp_ip.crf --depend .\objects\freertos_tcp_ip.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Reception.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Transmission.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_State_Handling.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Utils.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_Reception.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_tcp_reception.o --omf_browse .\objects\freertos_tcp_reception.crf --depend .\objects\freertos_tcp_reception.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Transmission.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Reception.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_State_Handling.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_tcp_state_handling.o --omf_browse .\objects\freertos_tcp_state_handling.crf --depend .\objects\freertos_tcp_state_handling.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Reception.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Transmission.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_State_Handling.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Utils.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_Transmission.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_tcp_transmission.o --omf_browse .\objects\freertos_tcp_transmission.crf --depend .\objects\freertos_tcp_transmission.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Reception.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Transmission.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_State_Handling.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Utils.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_Utils.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_tcp_utils.o --omf_browse .\objects\freertos_tcp_utils.crf --depend .\objects\freertos_tcp_utils.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_Utils.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_TCP_WIN.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_tcp_win.o --omf_browse .\objects\freertos_tcp_win.crf --depend .\objects\freertos_tcp_win.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_Tiny_TCP.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_tiny_tcp.o --omf_browse .\objects\freertos_tiny_tcp.crf --depend .\objects\freertos_tiny_tcp.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\FreeRTOS_UDP_IP.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\freertos_udp_ip.o --omf_browse .\objects\freertos_udp_ip.crf --depend .\objects\freertos_udp_ip.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\portable\BufferManagement\BufferAllocation_2.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\bufferallocation_2.o --omf_browse .\objects\bufferallocation_2.crf --depend .\objects\bufferallocation_2.d)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
F (.\Middlewares\FreeRTOS_TCP\portable\NetworkInterface\NetworkInterface.c)(0x688C2A66)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\networkinterface.o --omf_browse .\objects\networkinterface.crf --depend .\objects\networkinterface.d)
I (.\user\main.h)(0x688C29EC)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (.\user\uart0.h)(0x688C1A12)
I (.\user\trng.h)(0x688C1A12)
I (.\user\enet.h)(0x688C2E53)
F (.\user\main.c)(0x688C2EED)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (user\main.h)(0x688C29EC)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (user\uart0.h)(0x688C1A12)
I (user\trng.h)(0x688C1A12)
F (.\user\uart0.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\uart0.o --omf_browse .\objects\uart0.crf --depend .\objects\uart0.d)
I (user\uart0.h)(0x688C1A12)
I (user\main.h)(0x688C29EC)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
F (.\user\FreeRTOSConfig.h)(0x688C1A12)()
F (.\user\FreeRTOSIPConfig.h)(0x688C1A12)()
F (.\user\enet.c)(0x688C7083)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\enet.o --omf_browse .\objects\enet.crf --depend .\objects\enet.d)
I (user\enet.h)(0x688C2E53)
I (user\main.h)(0x688C29EC)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (user\uart0.h)(0x688C1A12)
F (.\user\trng.c)(0x688C1A12)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\CMSIS -I .\FWLib\Include -I .\Middlewares\FreeRTOS_Kernel\include -I .\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F -I .\user -I .\Middlewares\FreeRTOS_TCP\include -I .\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\trng.o --omf_browse .\objects\trng.crf --depend .\objects\trng.d)
I (user\trng.h)(0x688C1A12)
I (.\CMSIS\gd32f4xx.h)(0x688C1DB4)
I (.\CMSIS\core_cm4.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\CMSIS\core_cmInstr.h)(0x688C1A0D)
I (.\CMSIS\core_cmFunc.h)(0x688C1A0D)
I (.\CMSIS\core_cm4_simd.h)(0x688C1A0D)
I (.\CMSIS\system_gd32f4xx.h)(0x688C1A0D)
I (.\CMSIS\gd32f4xx_libopt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rcu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_adc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_can.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_crc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ctc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dac.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dbg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dci.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_dma.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_exti.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_fwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_gpio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_syscfg.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_i2c.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_iref.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_pmu.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_rtc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_sdio.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_spi.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_timer.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_trng.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_usart.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_wwdgt.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_misc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_enet.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\FWLib\Include\gd32f4xx_exmc.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_ipa.h)(0x688C1A0D)
I (.\FWLib\Include\gd32f4xx_tli.h)(0x688C1A0D)
I (user\main.h)(0x688C29EC)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\FreeRTOS.h)(0x688C1A0D)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (.\user\FreeRTOSConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\projdefs.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\portable.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\deprecated_definitions.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\portable\RVDS\ARM_CM4F\portmacro.h)(0x688C1A11)
I (.\Middlewares\FreeRTOS_Kernel\include\mpu_wrappers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\task.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\list.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP.h)(0x688C1A12)
I (.\user\FreeRTOSIPConfig.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOSIPConfigDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_errno_TCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\IPTraceMacroDefaults.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_start.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\portable\Compiler\Keil\pack_struct_end.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Utils.h)(0x688C1A12)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Middlewares\FreeRTOS_Kernel\include\queue.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\semphr.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Sockets.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_Kernel\include\event_groups.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_Kernel\include\timers.h)(0x688C1A0D)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_IP_Private.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_Stream_Buffer.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_WIN.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_TCP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_ARP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_UDP_IP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DHCP.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkInterface.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\NetworkBufferManagement.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Globals.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Callback.h)(0x688C1A12)
I (.\Middlewares\FreeRTOS_TCP\include\FreeRTOS_DNS_Cache.h)(0x688C1A12)
I (user\uart0.h)(0x688C1A12)
