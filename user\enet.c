#include "enet.h"
#include "gd32f4xx_enet.h"

#define configMAC_ADDR0		2
#define configMAC_ADDR1		0xA
#define configMAC_ADDR2		0xC
#define configMAC_ADDR3		0x5
#define configMAC_ADDR4		0x6
#define configMAC_ADDR5		0x6

extern enet_descriptors_struct	txdesc_tab[ENET_TXBUF_NUM];/*ENET TxDMA??????, ?????????DMX_RDTADDR???????*/
extern enet_descriptors_struct  rxdesc_tab[ENET_RXBUF_NUM];/*ENET RxDMA?????????????????DMX_TDTADDR???????*/

static void eth_rmii_gpio_conifg(RMII_GPIO_NUM gpio_periph,RMII_GPIO_PIN pin);
static void enet_gpio_config(void);
static void check_rmii_pins(void);


/*???????????(GPIOs, clocks, MAC, DMA, systick)*/
ErrStatus InitialiseNetwork(void)
{
    ErrStatus flag = ERROR;
    printf("Starting network initialization...\r\n");

    /*???????????????????*/
    nvic_irq_enable(ENET_IRQn, 6 , 0);
    printf("NVIC configured\r\n");

    enet_gpio_config();                             /* ??????????????GPIO??? */
    printf("GPIO configured\r\n");

    /* ???????????? */
    rcu_periph_clock_enable(RCU_ENET);
    rcu_periph_clock_enable(RCU_ENETTX);
    rcu_periph_clock_enable(RCU_ENETRX);
    printf("Ethernet clocks enabled\r\n");

    enet_deinit();  /* ??AHB??????????????? */
    printf("Ethernet deinitialized\r\n");

    enet_software_reset(); //????????CLK_TX??CLK_RX??????????????????

    // ??????????????
    uint32_t timeout = 0;
    while((ENET_DMA_BCTL & ENET_DMA_BCTL_SWR) && (timeout < 0x10000))
    {
        timeout++;
    }
    if(timeout >= 0x10000)
    {
        printf("Software reset timeout!\r\n");
        return ERROR;
    }
    printf("Software reset completed and verified\r\n");

    // ??enet_init??????PHY???
    printf("Checking PHY chip before enet_init...\r\n");
    uint16_t phy_id1 = 0, phy_id2 = 0;

    // ????????PHY???
    for(uint8_t addr = 0; addr < 32; addr++)
    {
        if(SUCCESS == enet_phy_write_read(ENET_PHY_READ, addr, 0x02, &phy_id1))
        {
            if(phy_id1 != 0x0000 && phy_id1 != 0xFFFF)
            {
                printf("Found PHY at address %d, ID1: 0x%04X\r\n", addr, phy_id1);
                if(SUCCESS == enet_phy_write_read(ENET_PHY_READ, addr, 0x03, &phy_id2))
                {
                    printf("PHY ID2: 0x%04X\r\n", phy_id2);

                    // ???LAN8720A??????????????
                    if(phy_id1 == 0x0007 && (phy_id2 & 0xFFF0) == 0xC0F0)
                    {
                        printf("Detected LAN8720A, configuring for REF_CLK output...\r\n");

                        // ????????PHY
                        printf("Resetting PHY...\r\n");
                        uint16_t reset_value = 0x8000;
                        enet_phy_write_read(ENET_PHY_WRITE, addr, 0x00, &reset_value);

                        // ??????????
                        uint32_t reset_timeout = 0;
                        uint16_t bcr = 0;
                        do {
                            enet_phy_write_read(ENET_PHY_READ, addr, 0x00, &bcr);
                            reset_timeout++;
                        } while((bcr & 0x8000) && (reset_timeout < 10000));

                        if(reset_timeout >= 10000)
                        {
                            printf("PHY reset timeout!\r\n");
                        }
                        else
                        {
                            printf("PHY reset completed\r\n");
                        }

                        // ????LAN8720A???REF_CLK
                        printf("Configuring LAN8720A for REF_CLK output...\r\n");

                        // ??????????????
                        uint16_t smr = 0;
                        if(SUCCESS == enet_phy_write_read(ENET_PHY_READ, addr, 0x12, &smr))
                        {
                            printf("Special Mode Register: 0x%04X\r\n", smr);
                        }

                        // ????????REF_CLK??? - ?????LAN8720A?????????????????
                        // ????1: ????????18 (0x12) ????
                        smr |= 0x0080; // ????REF_CLK????????
                        enet_phy_write_read(ENET_PHY_WRITE, addr, 0x12, &smr);

                        // ???PHY???
                        printf("Waiting for PHY stabilization...\r\n");
                        for(volatile uint32_t i = 0; i < 2000000; i++);

                        // ???????
                        if(SUCCESS == enet_phy_write_read(ENET_PHY_READ, addr, 0x12, &smr))
                        {
                            printf("Special Mode Register after config: 0x%04X\r\n", smr);
                        }
                    }
                }
                break;
            }
        }
    }

    /*?????????????IP??UDP??TCP??ICMP???????*/
    printf("Starting enet_init...\r\n");

    // ??????????????????????????
    printf("Trying 100M Full Duplex mode...\r\n");
    if (enet_init(ENET_100M_FULLDUPLEX, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS))
    {
        flag = SUCCESS;
        printf("enet_init SUCCESS (100M Full Duplex)\r\n");
    }
    else
    {
        printf("100M Full Duplex FAILED, trying 10M Full Duplex...\r\n");
        if (enet_init(ENET_10M_FULLDUPLEX, ENET_AUTOCHECKSUM_DROP_FAILFRAMES, ENET_BROADCAST_FRAMES_PASS))
        {
            flag = SUCCESS;
            printf("enet_init SUCCESS (10M Full Duplex)\r\n");
        }
        else
        {
            printf("All enet_init modes FAILED\r\n");

            // ????????MAC?????
            printf("=== MAC Debug Info ===\r\n");
            printf("MAC_CFG: 0x%08X\r\n", ENET_MAC_CFG);
            printf("DMA_BCTL: 0x%08X\r\n", ENET_DMA_BCTL);
            printf("DMA_STAT: 0x%08X\r\n", ENET_DMA_STAT);
            printf("MAC_FCTL: 0x%08X\r\n", ENET_MAC_FCTL);

            // 检查时钟配置
            printf("RCU_CFG0: 0x%08X\r\n", RCU_CFG0);
            printf("RCU_AHB1EN: 0x%08X\r\n", RCU_AHB1EN);
            printf("SYSCFG_CFG1: 0x%08X\r\n", SYSCFG_CFG1);
            printf("======================\r\n");

            return ERROR;
        }
    }
	
    enet_interrupt_enable(ENET_DMA_INT_NIE);        //???ENET MAC/MSC/DMA????????????????????
    enet_interrupt_enable(ENET_DMA_INT_RIE);        //????????????
    printf("Interrupts enabled\r\n");

    enet_mac_address_set(ENET_MAC_ADDRESS0,ucMACAddress);//????mac???
    printf("MAC address set: %02X:%02X:%02X:%02X:%02X:%02X\r\n",
           ucMACAddress[0], ucMACAddress[1], ucMACAddress[2],
           ucMACAddress[3], ucMACAddress[4], ucMACAddress[5]);

    enet_descriptors_chain_init(ENET_DMA_TX);//?????DMA????/???????????????
    enet_descriptors_chain_init(ENET_DMA_RX);
    printf("DMA descriptors initialized\r\n");

    for(uint8_t i=0; i<ENET_RXBUF_NUM; i++)//????????????????????ENET_DMA_STAT???????RS??
        enet_rx_desc_immediate_receive_complete_interrupt(&rxdesc_tab[i]);

    for(uint8_t i=0; i < ENET_TXBUF_NUM; i++)
        enet_transmit_checksum_config(&txdesc_tab[i], ENET_CHECKSUM_TCPUDPICMP_FULL);

    enet_enable();           	 //ENET Tx/Rx????????????ENET???????MAC??DMA???
    printf("Ethernet enabled\r\n");

    // ??????PHY??????
    uint16_t phy_status = 0;
    uint16_t phy_control = 0;
    uint16_t phy_id1_check = 0, phy_id2_check = 0;

    printf("=== PHY Status Check ===\r\n");

    // ???PHY ID
    if(SUCCESS == enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, 0x02, &phy_id1_check))
    {
        printf("PHY ID1: 0x%04X\r\n", phy_id1_check);
    }
    if(SUCCESS == enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, 0x03, &phy_id2_check))
    {
        printf("PHY ID2: 0x%04X\r\n", phy_id2_check);
    }

    // ???????????
    if(SUCCESS == enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, 0x00, &phy_control))
    {
        printf("PHY Control Register: 0x%04X\r\n", phy_control);
    }

    // ??????????
    if(SUCCESS == enet_phy_write_read(ENET_PHY_READ, PHY_ADDRESS, PHY_REG_BSR, &phy_status))
    {
        printf("PHY Status Register: 0x%04X\r\n", phy_status);

        // ??????????
        printf("Link Status: %s\r\n", (phy_status & 0x0004) ? "UP" : "DOWN");
        printf("Auto-negotiation: %s\r\n", (phy_status & 0x0020) ? "Complete" : "In Progress");
        printf("Remote Fault: %s\r\n", (phy_status & 0x0010) ? "Yes" : "No");

        // ????????????0xFFFF??????????
        if(phy_status == 0xFFFF)
        {
            printf("WARNING: PHY Status 0xFFFF indicates NO CABLE or read error!\r\n");
            printf("Network functions may not work properly.\r\n");
        }
        else if(!(phy_status & 0x0004))
        {
            printf("WARNING: PHY Link is DOWN - check cable connection!\r\n");
        }
    }
    else
    {
        printf("Failed to read PHY status\r\n");
    }

    printf("=== End PHY Check ===\r\n");

    return flag;
}
 /*??????????????????? vApplicationIPNetworkEventHook() ????????IP_task???????????  ethernet_task_creation ???????????*/


/*????RMII???*/
static void enet_gpio_config(void)
{
    rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_GPIOB);
    rcu_periph_clock_enable(RCU_GPIOC);
	
	/*PHY???????50MHz??????????MCU??????*/
    /* PHY???REF_CLK??????MCU??50MHz??????? */

    // PHY??REF_CLK??????????????MCU???????25MHz???
    printf("PHY REF_CLK unstable, using MCU 25MHz clock output...\r\n");
    gpio_af_set(CK_OUT0_GPIO_NUM, GPIO_AF_0, CK_OUT0_PIN);
    gpio_mode_set(CK_OUT0_GPIO_NUM, GPIO_MODE_AF, GPIO_PUPD_NONE, CK_OUT0_PIN);
    gpio_output_options_set(CK_OUT0_GPIO_NUM, GPIO_OTYPE_PP, GPIO_OSPEED_MAX, CK_OUT0_PIN);
    rcu_ckout0_config(RCU_CKOUT0SRC_HXTAL, RCU_CKOUT0_DIV1); // 25MHz???
    
	rcu_periph_clock_enable(RCU_SYSCFG);    							/* ???SYSCFG??? */
	syscfg_enet_phy_interface_config(SYSCFG_ENET_PHY_RMII);//?????????MAC??PHY???

	// ???????RMII????
	printf("Configuring RMII clock source...\r\n");
	// ???RMII????????
	SYSCFG_CFG1 |= SYSCFG_CFG1_ENET_PHY_SEL; // ???RMII??

	// ?????????
	printf("Waiting for clock stabilization...\r\n");
	for(volatile uint32_t i = 0; i < 2000000; i++); // ?????200ms

    // REF_CLK?????PA1???????????PHY?????50MHz???
    printf("Configuring PA1 as REF_CLK input from PHY...\r\n");
    // ????REF_CLK?????????????????????
    gpio_mode_set(RMII_REF_CLK_GPIO_NUM, GPIO_MODE_AF, GPIO_PUPD_PULLUP, RMII_REF_CLK_PIN); // ???????
    gpio_output_options_set(RMII_REF_CLK_GPIO_NUM, GPIO_OTYPE_PP, GPIO_OSPEED_MAX, RMII_REF_CLK_PIN);
    gpio_af_set(RMII_REF_CLK_GPIO_NUM, GPIO_AF_11, RMII_REF_CLK_PIN);
    // ??????????????????????????
    GPIO_ISTAT(RMII_REF_CLK_GPIO_NUM) |= RMII_REF_CLK_PIN;
	eth_rmii_gpio_conifg(RMII_MDIO_GPIO_NUM     , RMII_MDIO_PIN);
	eth_rmii_gpio_conifg(RMII_CRS_DV_GPIO_NUM   , RMII_CRS_DV_PIN);
	
	eth_rmii_gpio_conifg(RMII_TX_EN_GPIO_NUM    , RMII_TX_EN_PIN);
	eth_rmii_gpio_conifg(RMII_TXD0_GPIO_NUM     , RMII_TXD0_PIN);
    eth_rmii_gpio_conifg(RMII_TXD1_GPIO_NUM     , RMII_TXD1_PIN);
    
    eth_rmii_gpio_conifg(RMII_MDC_GPIO_NUM      , RMII_MDC_PIN);
	eth_rmii_gpio_conifg(RMII_RXD0_GPIO_NUM     , RMII_RXD0_PIN);
    eth_rmii_gpio_conifg(RMII_RXD1_GPIO_NUM     , RMII_RXD1_PIN);

    // ???????????
    check_rmii_pins();

    // ?????REF_CLK????????
    printf("=== REF_CLK Stability Test ===\r\n");
    uint32_t high_count = 0, low_count = 0;
    for(int i = 0; i < 10000; i++)
    {
        if(gpio_input_bit_get(RMII_REF_CLK_GPIO_NUM, RMII_REF_CLK_PIN))
            high_count++;
        else
            low_count++;
        for(volatile int j = 0; j < 10; j++); // ?????
    }
    printf("REF_CLK samples - High: %lu, Low: %lu\r\n", high_count, low_count);
    printf("High ratio: %lu%%\r\n", (high_count * 100) / (high_count + low_count));

    if(high_count == 0)
        printf("ERROR: REF_CLK stuck LOW!\r\n");
    else if(low_count == 0)
        printf("ERROR: REF_CLK stuck HIGH!\r\n");
    else if(high_count < 1000 || low_count < 1000)
        printf("WARNING: REF_CLK frequency may be too low!\r\n");
    else
        printf("REF_CLK appears to be toggling\r\n");
    printf("==============================\r\n");
}


/*????RMII?????????????? GPIO_AF_11 ?????????????????????????*/
static void eth_rmii_gpio_conifg(RMII_GPIO_NUM gpio_periph,RMII_GPIO_PIN pin)
{
    gpio_mode_set(gpio_periph, GPIO_MODE_AF, GPIO_PUPD_NONE, pin);
    gpio_output_options_set(gpio_periph, GPIO_OTYPE_PP, GPIO_OSPEED_MAX, pin);
    gpio_af_set(gpio_periph, GPIO_AF_11, pin);
}

/*???RMII????????*/
static void check_rmii_pins(void)
{
    printf("RMII Pin Configuration Check:\r\n");
    printf("GPIOA_CTL: 0x%08X\r\n", GPIO_CTL(GPIOA));
    printf("GPIOB_CTL: 0x%08X\r\n", GPIO_CTL(GPIOB));
    printf("GPIOC_CTL: 0x%08X\r\n", GPIO_CTL(GPIOC));
    printf("SYSCFG_CFG1: 0x%08X\r\n", SYSCFG_CFG1);

    // ????????????
    printf("=== RMII Pin Details ===\r\n");
    printf("PA1 (REF_CLK): Input=%d\r\n", gpio_input_bit_get(GPIOA, GPIO_PIN_1));
    printf("PA2 (MDIO): Input=%d\r\n", gpio_input_bit_get(GPIOA, GPIO_PIN_2));
    printf("PA7 (CRS_DV): Input=%d\r\n", gpio_input_bit_get(GPIOA, GPIO_PIN_7));
    printf("PC4 (RXD0): Input=%d\r\n", gpio_input_bit_get(GPIOC, GPIO_PIN_4));
    printf("PC5 (RXD1): Input=%d\r\n", gpio_input_bit_get(GPIOC, GPIO_PIN_5));
    printf("========================\r\n");
}

