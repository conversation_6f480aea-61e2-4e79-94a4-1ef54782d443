<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Aug 01 17:22:14 2025
<BR><P>
<H3>Maximum Stack Usage =        880 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
http_server_task &rArr; FreeRTOS_send &rArr; prvTCPSendCheck &rArr; prvTCPCreateStream &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[9]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">DebugMon_Handler</a><BR>
 <LI><a href="#[1e]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1e]">ADC_IRQHandler</a><BR>
 <LI><a href="#[df]">FreeRTOS_listen</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e3]">vTCPStateChange</a><BR>
 <LI><a href="#[1b1]">prvTCPSetSocketCount</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[184]">vSocketClose</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1e]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">DebugMon_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">ENET_IRQHandler</a> from networkinterface.o(i.ENET_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">EXTI10_15_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[d]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">PendSV_Handler</a> from port.o(.emb_text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">SVC_Handler</a> from port.o(.emb_text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">SysTick_Handler</a> from port.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[e]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">TIMER2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[c]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[74]">__main</a> from __main.o(!!!main) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[68]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[67]">_snputc</a> from _snputc.o(.text) referenced from noretval__2snprintf.o(.text)
 <LI><a href="#[71]">clock_test_task</a> from main.o(i.clock_test_task) referenced from networkinterface.o(i.vApplicationIPNetworkEventHook)
 <LI><a href="#[6c]">enet_inti_task</a> from main.o(i.enet_inti_task) referenced from main.o(i.main)
 <LI><a href="#[6b]">eth_rece_data_task</a> from networkinterface.o(i.eth_rece_data_task) referenced from networkinterface.o(i.ethernet_task_creation)
 <LI><a href="#[69]">fputc</a> from uart0.o(i.fputc) referenced from _printf_char_file.o(.text)
 <LI><a href="#[70]">http_server_task</a> from main.o(i.http_server_task) referenced from networkinterface.o(i.vApplicationIPNetworkEventHook)
 <LI><a href="#[6a]">prvIPTask</a> from freertos_ip.o(i.prvIPTask) referenced from freertos_ip.o(i.FreeRTOS_IPInit)
 <LI><a href="#[72]">prvIdleTask</a> from tasks.o(i.prvIdleTask) referenced from tasks.o(i.vTaskStartScheduler)
 <LI><a href="#[6e]">prvTaskExitError</a> from port.o(i.prvTaskExitError) referenced from port.o(i.pxPortInitialiseStack)
 <LI><a href="#[73]">prvTimerTask</a> from timers.o(i.prvTimerTask) referenced from timers.o(i.xTimerCreateTimerTask)
 <LI><a href="#[6d]">start_task</a> from main.o(i.start_task) referenced from main.o(i.main)
 <LI><a href="#[6f]">tcp_server_task</a> from main.o(i.tcp_server_task) referenced from networkinterface.o(i.vApplicationIPNetworkEventHook)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[74]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[75]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[77]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[203]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[204]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[78]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[205]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[79]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[9b]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[7b]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[7c]"></a>_printf_x</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_x &rArr; _printf_int_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[7e]"></a>_printf_c</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_c.o(.ARM.Collect$$_printf_percent$$00000013))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_c &rArr; _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[80]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[206]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[87]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[82]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[207]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[208]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[209]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[20a]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[20b]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[20c]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[20d]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[20e]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[20f]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[210]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[211]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[212]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[213]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[214]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[215]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[216]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[217]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[218]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[219]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[21a]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[8c]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[21b]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[21c]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[21d]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[21e]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[21f]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[220]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[221]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[222]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[76]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[223]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[84]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[86]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[224]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[88]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 192 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[225]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[aa]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[8b]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[226]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[8d]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[8]"></a>SVC_Handler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1fe]"></a>__asm___6_port_c_39a90d8d__prvStartFirstTask</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[1fd]"></a>__asm___6_port_c_39a90d8d__prvEnableVFP</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[a]"></a>PendSV_Handler</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[227]"></a>vPortGetIPSR</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, port.o(.emb_text), UNUSED)

<P><STRONG><a name="[2]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a9]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[228]"></a>__use_no_semihosting</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi_2.o(.text), UNUSED)

<P><STRONG><a name="[90]"></a>__2printf</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, noretval__2printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
<LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationIPNetworkEventHook
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceInitialise
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_task
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_test_task
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_task_creation
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rece_data_task
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_ready_check
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_rmii_pins
</UL>

<P><STRONG><a name="[92]"></a>__2snprintf</STRONG> (Thumb, 48 bytes, Stack size 32 bytes, noretval__2snprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPFlagMeaning
</UL>

<P><STRONG><a name="[96]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>

<P><STRONG><a name="[97]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>

<P><STRONG><a name="[95]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[7a]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[7d]"></a>_printf_int_hex</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, _printf_hex_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_int_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[229]"></a>_printf_longlong_hex</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, _printf_hex_int.o(.text), UNUSED)

<P><STRONG><a name="[99]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[113]"></a>memcmp</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPRefreshCacheEntry
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPProcessPacket
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eConsiderFrameForProcessing
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAllowIPPacket
</UL>

<P><STRONG><a name="[172]"></a>strcpy</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, strcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertCacheEntry
</UL>

<P><STRONG><a name="[130]"></a>strlen</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, strlen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertCacheEntry
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
</UL>

<P><STRONG><a name="[9f]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxDuplicateNetworkBufferWithDescriptor
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPGenerateRequestPacket
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessGeneratedUDPPacket
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGet
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferAdd
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPBufferResize
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetOptions
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memmove
</UL>

<P><STRONG><a name="[9c]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[22a]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[18a]"></a>__aeabi_memmove</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memmove_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPPacket
</UL>

<P><STRONG><a name="[9e]"></a>__rt_memmove</STRONG> (Thumb, 132 bytes, Stack size 0 bytes, rt_memmove_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__memmove_aligned
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>

<P><STRONG><a name="[22b]"></a>__memmove_lastfew</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memmove_v6.o(.text), UNUSED)

<P><STRONG><a name="[9d]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memmove_w
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[22c]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[22d]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[22e]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[a1]"></a>__aeabi_memset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, aeabi_memset.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[e2]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_listen
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareConnect
</UL>

<P><STRONG><a name="[a3]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[a2]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[bc]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_ClearARP
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_listen
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateStream
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCreateSectors
</UL>

<P><STRONG><a name="[22f]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[230]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[a4]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[153]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvFindEntryIndex
</UL>

<P><STRONG><a name="[231]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[232]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[233]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[234]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[235]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[98]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[93]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>

<P><STRONG><a name="[94]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>

<P><STRONG><a name="[67]"></a>_snputc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _snputc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> noretval__2snprintf.o(.text)
</UL>
<P><STRONG><a name="[a5]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[7f]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[81]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[91]"></a>_printf_char_file</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, _printf_char_file.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ferror
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[236]"></a>__aeabi_memmove4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memmove_w.o(.text), UNUSED)

<P><STRONG><a name="[237]"></a>__aeabi_memmove8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memmove_w.o(.text), UNUSED)

<P><STRONG><a name="[a7]"></a>__rt_memmove_w</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, rt_memmove_w.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[a0]"></a>__memmove_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memmove_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memmove
</UL>

<P><STRONG><a name="[238]"></a>__memmove_lastfew_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memmove_w.o(.text), UNUSED)

<P><STRONG><a name="[a6]"></a>ferror</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ferror.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[85]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[8a]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[239]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[a8]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[23a]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[6]"></a>BusFault_Handler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = BusFault_Handler &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[ab]"></a>DNS_ParseDNSReply</STRONG> (Thumb, 694 bytes, Stack size 112 bytes, freertos_dns_parser.o(i.DNS_ParseDNSReply))
<BR><BR>[Stack]<UL><LI>Max Depth = 316<LI>Call Chain = DNS_ParseDNSReply &rArr; parseDNSAnswer &rArr; FreeRTOS_dns_update &rArr; FreeRTOS_ProcessDNSCache &rArr; prvInsertCacheEntry &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxDuplicateNetworkBufferWithDescriptor
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xApplicationDNSQueryHook
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReturnEthernetFrame
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usChar2u16
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxUDPPayloadBuffer_to_NetworkBuffer
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prepareReplyDNSMessage
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDNSAnswer
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_SkipNameField
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ReadNameField
</UL>
<BR>[Called By]<UL><LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulDNSHandlePacket
</UL>

<P><STRONG><a name="[ac]"></a>DNS_ReadNameField</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, freertos_dns_parser.o(i.DNS_ReadNameField))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DNS_ReadNameField
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
</UL>

<P><STRONG><a name="[ad]"></a>DNS_SkipNameField</STRONG> (Thumb, 80 bytes, Stack size 12 bytes, freertos_dns_parser.o(i.DNS_SkipNameField))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DNS_SkipNameField
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDNSAnswer
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
</UL>

<P><STRONG><a name="[b6]"></a>DNS_TreatNBNS</STRONG> (Thumb, 514 bytes, Stack size 88 bytes, freertos_dns_parser.o(i.DNS_TreatNBNS))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = DNS_TreatNBNS &rArr; pxDuplicateNetworkBufferWithDescriptor &rArr; pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxDuplicateNetworkBufferWithDescriptor
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xApplicationDNSQueryHook
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReturnEthernetFrame
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usChar2u16
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxUDPPayloadBuffer_to_NetworkBuffer
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prepareReplyDNSMessage
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_dns_update
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulNBNSHandlePacket
</UL>

<P><STRONG><a name="[49]"></a>ENET_IRQHandler</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, networkinterface.o(i.ENET_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ENET_IRQHandler &rArr; vTaskGenericNotifyGiveFromISR
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_interrupt_flag_get
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_interrupt_flag_clear
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskGenericNotifyGiveFromISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[bb]"></a>FreeRTOS_ClearARP</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, freertos_arp.o(i.FreeRTOS_ClearARP))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = FreeRTOS_ClearARP &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessNetworkDownEvent
</UL>

<P><STRONG><a name="[1e0]"></a>FreeRTOS_GetAddressConfiguration</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, freertos_ip.o(i.FreeRTOS_GetAddressConfiguration))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FreeRTOS_GetAddressConfiguration
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationIPNetworkEventHook
</UL>

<P><STRONG><a name="[1f9]"></a>FreeRTOS_GetIPTaskHandle</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, freertos_ip.o(i.FreeRTOS_GetIPTaskHandle))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIsCallingFromIPTask
</UL>

<P><STRONG><a name="[1b3]"></a>FreeRTOS_GetTCPStateName</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, freertos_tcp_state_handling.o(i.FreeRTOS_GetTCPStateName))
<BR><BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPStatusAgeCheck
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
</UL>

<P><STRONG><a name="[bd]"></a>FreeRTOS_IPInit</STRONG> (Thumb, 274 bytes, Stack size 40 bytes, freertos_ip.o(i.FreeRTOS_IPInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = FreeRTOS_IPInit &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkBuffersInitialise
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPreCheckConfigs
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vNetworkSocketsInit
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_inti_task
</UL>

<P><STRONG><a name="[c6]"></a>FreeRTOS_NetworkDown</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, freertos_ip.o(i.FreeRTOS_NetworkDown))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = FreeRTOS_NetworkDown &rArr; xSendEventStructToIPTask &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessNetworkDownEvent
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTask
</UL>

<P><STRONG><a name="[c8]"></a>FreeRTOS_OutputARPRequest</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, freertos_arp.o(i.FreeRTOS_OutputARPRequest))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = FreeRTOS_OutputARPRequest &rArr; pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceOutput
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIsCallingFromIPTask
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPGenerateRequestPacket
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xCheckRequiresARPResolution
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPAgeCache
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPProcessPacket
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareConnect
</UL>

<P><STRONG><a name="[cd]"></a>FreeRTOS_ProcessDNSCache</STRONG> (Thumb, 178 bytes, Stack size 40 bytes, freertos_dns_cache.o(i.FreeRTOS_ProcessDNSCache))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = FreeRTOS_ProcessDNSCache &rArr; prvInsertCacheEntry &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUpdateCacheEntry
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertCacheEntry
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetCacheIPEntry
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvFindEntryIndex
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_dns_update
</UL>

<P><STRONG><a name="[d3]"></a>FreeRTOS_accept</STRONG> (Thumb, 280 bytes, Stack size 56 bytes, freertos_sockets.o(i.FreeRTOS_accept))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = FreeRTOS_accept &rArr; xSendEventStructToIPTask &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSetTimeOutState
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvValidSocket
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
</UL>

<P><STRONG><a name="[da]"></a>FreeRTOS_bind</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, freertos_sockets.o(i.FreeRTOS_bind))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = FreeRTOS_bind &rArr; xSendEventStructToIPTask &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
</UL>

<P><STRONG><a name="[db]"></a>FreeRTOS_closesocket</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, freertos_sockets.o(i.FreeRTOS_closesocket))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = FreeRTOS_closesocket &rArr; xSendEventStructToIPTask &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
</UL>

<P><STRONG><a name="[b7]"></a>FreeRTOS_dns_update</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, freertos_dns_cache.o(i.FreeRTOS_dns_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = FreeRTOS_dns_update &rArr; FreeRTOS_ProcessDNSCache &rArr; prvInsertCacheEntry &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_ProcessDNSCache
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDNSAnswer
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_TreatNBNS
</UL>

<P><STRONG><a name="[de]"></a>FreeRTOS_inet_ntoa</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, freertos_sockets.o(i.FreeRTOS_inet_ntoa))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = FreeRTOS_inet_ntoa
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationIPNetworkEventHook
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_inet_ntop4
</UL>

<P><STRONG><a name="[dc]"></a>FreeRTOS_inet_ntop</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, freertos_sockets.o(i.FreeRTOS_inet_ntop))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = FreeRTOS_inet_ntop &rArr; FreeRTOS_inet_ntop4 &rArr; FreeRTOS_inet_ntoa
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_inet_ntop4
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDNSAnswer
</UL>

<P><STRONG><a name="[dd]"></a>FreeRTOS_inet_ntop4</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, freertos_sockets.o(i.FreeRTOS_inet_ntop4))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = FreeRTOS_inet_ntop4 &rArr; FreeRTOS_inet_ntoa
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_inet_ntoa
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_inet_ntop
</UL>

<P><STRONG><a name="[df]"></a>FreeRTOS_listen</STRONG> (Thumb, 144 bytes, Stack size 24 bytes, freertos_sockets.o(i.FreeRTOS_listen))
<BR><BR>[Stack]<UL><LI>Max Depth = 36 + In Cycle
<LI>Call Chain = FreeRTOS_listen &rArr;  vTCPStateChange (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vStreamBufferClear
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvValidSocket
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_int32
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketListenNextTime
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
</UL>

<P><STRONG><a name="[f7]"></a>FreeRTOS_max_uint32</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, freertos_ip_utils.o(i.FreeRTOS_max_uint32))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
</UL>

<P><STRONG><a name="[e0]"></a>FreeRTOS_min_int32</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, freertos_ip_utils.o(i.FreeRTOS_min_int32))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckRxData
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_listen
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxAdd_FrontSegment
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowTxAdd
</UL>

<P><STRONG><a name="[1dc]"></a>FreeRTOS_min_size_t</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, freertos_ip_utils.o(i.FreeRTOS_min_size_t))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetPtr
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGet
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferAdd
</UL>

<P><STRONG><a name="[19f]"></a>FreeRTOS_min_uint32</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, freertos_ip_utils.o(i.FreeRTOS_min_uint32))
<BR><BR>[Called By]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxHasSpace
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSocketSetMSS
</UL>

<P><STRONG><a name="[e4]"></a>FreeRTOS_recv</STRONG> (Thumb, 348 bytes, Stack size 64 bytes, freertos_sockets.o(i.FreeRTOS_recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = FreeRTOS_recv &rArr; xSendEventToIPTask &rArr; xSendEventStructToIPTask &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSetTimeOutState
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventToIPTask
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetPtr
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSize
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGet
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferFrontSpace
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvValidSocket
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
</UL>

<P><STRONG><a name="[f6]"></a>FreeRTOS_round_up</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, freertos_ip_utils.o(i.FreeRTOS_round_up))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
</UL>

<P><STRONG><a name="[ea]"></a>FreeRTOS_rx_size</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, freertos_sockets.o(i.FreeRTOS_rx_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = FreeRTOS_rx_size &rArr; uxStreamBufferGetSize &rArr; uxStreamBufferDistance
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSize
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketSelect
</UL>

<P><STRONG><a name="[eb]"></a>FreeRTOS_send</STRONG> (Thumb, 286 bytes, Stack size 64 bytes, freertos_sockets.o(i.FreeRTOS_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = FreeRTOS_send &rArr; prvTCPSendCheck &rArr; prvTCPCreateStream &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSetTimeOutState
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventToIPTask
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIsCallingFromIPTask
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSpace
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferAdd
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
</UL>

<P><STRONG><a name="[ef]"></a>FreeRTOS_socket</STRONG> (Thumb, 240 bytes, Stack size 40 bytes, freertos_sockets.o(i.FreeRTOS_socket))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = FreeRTOS_socket &rArr; xEventGroupCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupCreate
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDetermineSocketSize
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_round_up
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_max_uint32
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
</UL>

<P><STRONG><a name="[f8]"></a>FreeRTOS_tx_space</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, freertos_sockets.o(i.FreeRTOS_tx_space))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = FreeRTOS_tx_space &rArr; uxStreamBufferGetSpace &rArr; uxStreamBufferSpace
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketSelect
</UL>

<P><STRONG><a name="[4]"></a>HardFault_Handler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = HardFault_Handler &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f9]"></a>InitialiseNetwork</STRONG> (Thumb, 2090 bytes, Stack size 64 bytes, enet.o(i.InitialiseNetwork))
<BR><BR>[Stack]<UL><LI>Max Depth = 240 + Unknown Stack Size
<LI>Call Chain = InitialiseNetwork &rArr; enet_gpio_config &rArr; check_rmii_pins &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_transmit_checksum_config
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_software_reset
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rx_desc_immediate_receive_complete_interrupt
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_write_read
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_mac_address_set
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_interrupt_enable
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_enable
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_descriptors_chain_init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_deinit
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceInitialise
</UL>

<P><STRONG><a name="[5]"></a>MemManage_Handler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = MemManage_Handler &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>NMI_Handler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 152 + Unknown Stack Size
<LI>Call Chain = NMI_Handler &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[107]"></a>ProcessICMPPacket</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, freertos_icmp.o(i.ProcessICMPPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = ProcessICMPPacket &rArr; prvProcessICMPEchoRequest &rArr; usGenerateProtocolChecksum &rArr; usGenerateChecksum
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessICMPEchoRequest
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPPacket
</UL>

<P><STRONG><a name="[b]"></a>SysTick_Handler</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, port.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SysTick_Handler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>SystemInit</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[7]"></a>UsageFault_Handler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = UsageFault_Handler &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9a]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[8e]"></a>_sys_exit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, uart0.o(i._sys_exit))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[71]"></a>clock_test_task</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, main.o(i.clock_test_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 152 + Unknown Stack Size
<LI>Call Chain = clock_test_task &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> networkinterface.o(i.vApplicationIPNetworkEventHook)
</UL>
<P><STRONG><a name="[10e]"></a>eARPGetCacheEntry</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, freertos_arp.o(i.eARPGetCacheEntry))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = eARPGetCacheEntry &rArr; prvCacheLookup
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIsIPv4Multicast
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSetMultiCastIPv4MacAddress
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCacheLookup
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessGeneratedUDPPacket
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareConnect
</UL>

<P><STRONG><a name="[112]"></a>eARPProcessPacket</STRONG> (Thumb, 328 bytes, Stack size 40 bytes, freertos_arp.o(i.eARPProcessPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = eARPProcessPacket &rArr; FreeRTOS_OutputARPRequest &rArr; pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSetTimeOutState
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPRefreshCacheEntry
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessARPPacketReply
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessEthernetPacket
</UL>

<P><STRONG><a name="[116]"></a>eConsiderFrameForProcessing</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, freertos_ip.o(i.eConsiderFrameForProcessing))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = eConsiderFrameForProcessing &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rece_data_task
</UL>

<P><STRONG><a name="[fd]"></a>enet_deinit</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, gd32f4xx_enet.o(i.enet_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = enet_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_initpara_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[103]"></a>enet_descriptors_chain_init</STRONG> (Thumb, 162 bytes, Stack size 28 bytes, gd32f4xx_enet.o(i.enet_descriptors_chain_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = enet_descriptors_chain_init
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[106]"></a>enet_enable</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, gd32f4xx_enet.o(i.enet_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = enet_enable &rArr; enet_tx_enable &rArr; enet_txfifo_flush
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_tx_enable
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rx_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[12c]"></a>enet_frame_receive</STRONG> (Thumb, 236 bytes, Stack size 12 bytes, gd32f4xx_enet.o(i.enet_frame_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = enet_frame_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rece_data_task
</UL>

<P><STRONG><a name="[1fb]"></a>enet_frame_transmit</STRONG> (Thumb, 196 bytes, Stack size 16 bytes, gd32f4xx_enet.o(i.enet_frame_transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = enet_frame_transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceOutput
</UL>

<P><STRONG><a name="[100]"></a>enet_init</STRONG> (Thumb, 822 bytes, Stack size 56 bytes, gd32f4xx_enet.o(i.enet_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = enet_init &rArr; enet_phy_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_write_read
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_config
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_delay
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_default_init
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[119]"></a>enet_initpara_reset</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_initpara_reset))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_deinit
</UL>

<P><STRONG><a name="[101]"></a>enet_interrupt_enable</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[ba]"></a>enet_interrupt_flag_clear</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_IRQHandler
</UL>

<P><STRONG><a name="[b8]"></a>enet_interrupt_flag_get</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_IRQHandler
</UL>

<P><STRONG><a name="[6c]"></a>enet_inti_task</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, main.o(i.enet_inti_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = enet_inti_task &rArr; FreeRTOS_IPInit &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[102]"></a>enet_mac_address_set</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_mac_address_set))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[122]"></a>enet_phy_config</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, gd32f4xx_enet.o(i.enet_phy_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = enet_phy_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_write_read
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_init
</UL>

<P><STRONG><a name="[ff]"></a>enet_phy_write_read</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, gd32f4xx_enet.o(i.enet_phy_write_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = enet_phy_write_read
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_config
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_task
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[104]"></a>enet_rx_desc_immediate_receive_complete_interrupt</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[11b]"></a>enet_rx_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_rx_enable))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_enable
</UL>

<P><STRONG><a name="[129]"></a>enet_rxframe_drop</STRONG> (Thumb, 158 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_rxframe_drop))
<BR><BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rxframe_size_get
</UL>

<P><STRONG><a name="[128]"></a>enet_rxframe_size_get</STRONG> (Thumb, 138 bytes, Stack size 4 bytes, gd32f4xx_enet.o(i.enet_rxframe_size_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = enet_rxframe_size_get
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rxframe_drop
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rece_data_task
</UL>

<P><STRONG><a name="[fe]"></a>enet_software_reset</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, gd32f4xx_enet.o(i.enet_software_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = enet_software_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[105]"></a>enet_transmit_checksum_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_transmit_checksum_config))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[11a]"></a>enet_tx_enable</STRONG> (Thumb, 32 bytes, Stack size 4 bytes, gd32f4xx_enet.o(i.enet_tx_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = enet_tx_enable &rArr; enet_txfifo_flush
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_txfifo_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_enable
</UL>

<P><STRONG><a name="[12a]"></a>enet_txfifo_flush</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, gd32f4xx_enet.o(i.enet_txfifo_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = enet_txfifo_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_tx_enable
</UL>

<P><STRONG><a name="[6b]"></a>eth_rece_data_task</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, networkinterface.o(i.eth_rece_data_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = eth_rece_data_task &rArr; pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_rxframe_size_get
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_frame_receive
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskGenericNotifyTake
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eConsiderFrameForProcessing
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> networkinterface.o(i.ethernet_task_creation)
</UL>
<P><STRONG><a name="[12d]"></a>ethernet_task_creation</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, networkinterface.o(i.ethernet_task_creation))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = ethernet_task_creation &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationIPNetworkEventHook
</UL>

<P><STRONG><a name="[69]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, uart0.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_file.o(.text)
</UL>
<P><STRONG><a name="[11c]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rmii_gpio_conifg
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
</UL>

<P><STRONG><a name="[10c]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_test_task
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_rmii_pins
</UL>

<P><STRONG><a name="[11d]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rmii_gpio_conifg
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
</UL>

<P><STRONG><a name="[11e]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rmii_gpio_conifg
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
</UL>

<P><STRONG><a name="[70]"></a>http_server_task</STRONG> (Thumb, 220 bytes, Stack size 560 bytes, main.o(i.http_server_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 880 + Unknown Stack Size
<LI>Call Chain = http_server_task &rArr; FreeRTOS_send &rArr; prvTCPSendCheck &rArr; prvTCPCreateStream &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_closesocket
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_bind
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_listen
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_accept
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> networkinterface.o(i.vApplicationIPNetworkEventHook)
</UL>
<P><STRONG><a name="[131]"></a>lTCPAddRxdata</STRONG> (Thumb, 184 bytes, Stack size 48 bytes, freertos_sockets.o(i.lTCPAddRxdata))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = lTCPAddRxdata &rArr; prvTCPCreateStream &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventToIPTask
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferFrontSpace
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferAdd
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateStream
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvStoreRxData
</UL>

<P><STRONG><a name="[133]"></a>lTCPWindowRxCheck</STRONG> (Thumb, 186 bytes, Stack size 64 bytes, freertos_tcp_win.o(i.lTCPWindowRxCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = lTCPWindowRxCheck &rArr; prvTCPWindowRx_UnexpectedRX &rArr; xTCPWindowNew &rArr; vListInsertFifo
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowRx_UnexpectedRX
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowRx_ExpectedRX
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvStoreRxData
</UL>

<P><STRONG><a name="[136]"></a>lTCPWindowTxAdd</STRONG> (Thumb, 190 bytes, Stack size 40 bytes, freertos_tcp_win.o(i.lTCPWindowTxAdd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = lTCPWindowTxAdd &rArr; xTCPWindowNew &rArr; vListInsertFifo
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_int32
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowNew
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertFifo
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxAdd_FrontSegment
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPIncrementTxPosition
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPAddTxData
</UL>

<P><STRONG><a name="[89]"></a>main</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 192 + Unknown Stack Size
<LI>Call Chain = main &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_init
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[fa]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[13b]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[af]"></a>parseDNSAnswer</STRONG> (Thumb, 302 bytes, Stack size 112 bytes, freertos_dns_parser.o(i.parseDNSAnswer))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = parseDNSAnswer &rArr; FreeRTOS_dns_update &rArr; FreeRTOS_ProcessDNSCache &rArr; prvInsertCacheEntry &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usChar2u16
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_inet_ntop
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_SkipNameField
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_dns_update
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
</UL>

<P><STRONG><a name="[b3]"></a>prepareReplyDNSMessage</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, freertos_dns_parser.o(i.prepareReplyDNSMessage))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = prepareReplyDNSMessage &rArr; usGenerateProtocolChecksum &rArr; usGenerateChecksum
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateProtocolChecksum
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateChecksum
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_TreatNBNS
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
</UL>

<P><STRONG><a name="[14a]"></a>prvCheckOptions</STRONG> (Thumb, 148 bytes, Stack size 56 bytes, freertos_tcp_reception.o(i.prvCheckOptions))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = prvCheckOptions &rArr; prvSingleStepTCPHeaderOptions &rArr; prvReadSackOption &rArr; ulTCPWindowTxSack &rArr; prvTCPWindowTxCheckAck &rArr; prvTCPWindowTxCheckAck_CalcSRTT &rArr; ulTimerGetAge
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSingleStepTCPHeaderOptions
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
</UL>

<P><STRONG><a name="[14c]"></a>prvCheckRxData</STRONG> (Thumb, 146 bytes, Stack size 56 bytes, freertos_tcp_reception.o(i.prvCheckRxData))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = prvCheckRxData
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_int32
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
</UL>

<P><STRONG><a name="[160]"></a>prvHandleListen</STRONG> (Thumb, 314 bytes, Stack size 32 bytes, freertos_tcp_state_handling.o(i.prvHandleListen))
<BR><BR>[Stack]<UL><LI>Max Depth = 344 + Unknown Stack Size
<LI>Call Chain = prvHandleListen &rArr; prvTCPSendReset &rArr; prvTCPSendSpecialPacketHelper &rArr; prvTCPReturnPacket &rArr; xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSocketCopy
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendReset
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulApplicationGetNextSequenceNumber
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateWindow
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSocketSetMSS
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
</UL>

<P><STRONG><a name="[180]"></a>prvProcessNetworkDownEvent</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, freertos_ip_utils.o(i.prvProcessNetworkDownEvent))
<BR><BR>[Stack]<UL><LI>Max Depth = 256 + Unknown Stack Size
<LI>Call Chain = prvProcessNetworkDownEvent &rArr; xNetworkInterfaceInitialise &rArr; InitialiseNetwork &rArr; enet_gpio_config &rArr; check_rmii_pins &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_ClearARP
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPSetARPTimerEnableState
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationIPNetworkEventHook
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPNetworkUpCalls
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_NetworkDown
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[19a]"></a>prvSendData</STRONG> (Thumb, 308 bytes, Stack size 80 bytes, freertos_tcp_transmission.o(i.prvSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = prvSendData &rArr; prvTCPReturnPacket &rArr; xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
</UL>

<P><STRONG><a name="[19c]"></a>prvSetOptions</STRONG> (Thumb, 254 bytes, Stack size 48 bytes, freertos_tcp_transmission.o(i.prvSetOptions))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = prvSetOptions
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
</UL>

<P><STRONG><a name="[19d]"></a>prvSetSynAckOptions</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, freertos_tcp_transmission.o(i.prvSetSynAckOptions))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvSetSynAckOptions &rArr; prvWinScaleFactor
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvWinScaleFactor
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendPacket
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
</UL>

<P><STRONG><a name="[164]"></a>prvSocketSetMSS</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, freertos_tcp_utils.o(i.prvSocketSetMSS))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvSocketSetMSS
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_uint32
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareConnect
</UL>

<P><STRONG><a name="[1a0]"></a>prvStoreRxData</STRONG> (Thumb, 226 bytes, Stack size 72 bytes, freertos_tcp_reception.o(i.prvStoreRxData))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = prvStoreRxData &rArr; prvTCPSendReset &rArr; prvTCPSendSpecialPacketHelper &rArr; prvTCPReturnPacket &rArr; xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowRxCheck
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendReset
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSpace
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPAddRxdata
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
</UL>

<P><STRONG><a name="[159]"></a>prvTCPAddTxData</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, freertos_tcp_transmission.o(i.prvTCPAddTxData))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = prvTCPAddTxData &rArr; lTCPWindowTxAdd &rArr; xTCPWindowNew &rArr; vListInsertFifo
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vStreamBufferMoveMid
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferMidSpace
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowTxAdd
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEstablished
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPSocketCheck
</UL>

<P><STRONG><a name="[1a3]"></a>prvTCPBufferResize</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, freertos_tcp_transmission.o(i.prvTCPBufferResize))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = prvTCPBufferResize &rArr; pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
</UL>

<P><STRONG><a name="[165]"></a>prvTCPCreateWindow</STRONG> (Thumb, 86 bytes, Stack size 32 bytes, freertos_tcp_transmission.o(i.prvTCPCreateWindow))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = prvTCPCreateWindow &rArr; vTCPWindowCreate &rArr; prvCreateSectors &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareConnect
</UL>

<P><STRONG><a name="[1a5]"></a>prvTCPFlagMeaning</STRONG> (Thumb, 136 bytes, Stack size 40 bytes, freertos_tcp_utils.o(i.prvTCPFlagMeaning))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = prvTCPFlagMeaning &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
</UL>

<P><STRONG><a name="[1a6]"></a>prvTCPHandleState</STRONG> (Thumb, 440 bytes, Stack size 64 bytes, freertos_tcp_state_handling.o(i.prvTCPHandleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = prvTCPHandleState &rArr; prvHandleEstablished &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleFin
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleSynReceived
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEstablished
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvStoreRxData
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckRxData
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetSynAckOptions
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetOptions
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSendData
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
</UL>

<P><STRONG><a name="[15d]"></a>prvTCPPrepareSend</STRONG> (Thumb, 596 bytes, Stack size 80 bytes, freertos_tcp_transmission.o(i.prvTCPPrepareSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferDistance
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGet
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxGet
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPBufferResize
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowTxDone
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEstablished
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendRepeated
</UL>

<P><STRONG><a name="[19b]"></a>prvTCPReturnPacket</STRONG> (Thumb, 720 bytes, Stack size 128 bytes, freertos_tcp_transmission.o(i.prvTCPReturnPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = prvTCPReturnPacket &rArr; xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceOutput
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPGetCacheEntry
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateProtocolChecksum
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateChecksum
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferFrontSpace
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_uint32
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendRepeated
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendPacket
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPSocketCheck
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendSpecialPacketHelper
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSendData
</UL>

<P><STRONG><a name="[1ad]"></a>prvTCPSendChallengeAck</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, freertos_tcp_transmission.o(i.prvTCPSendChallengeAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = prvTCPSendChallengeAck &rArr; prvTCPSendSpecialPacketHelper &rArr; prvTCPReturnPacket &rArr; xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendSpecialPacketHelper
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
</UL>

<P><STRONG><a name="[1af]"></a>prvTCPSendPacket</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, freertos_tcp_transmission.o(i.prvTCPSendPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 360 + Unknown Stack Size
<LI>Call Chain = prvTCPSendPacket &rArr; prvTCPSendRepeated &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendRepeated
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPMakeSurePrepared
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetSynAckOptions
</UL>
<BR>[Called By]<UL><LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPSocketCheck
</UL>

<P><STRONG><a name="[1b0]"></a>prvTCPSendRepeated</STRONG> (Thumb, 62 bytes, Stack size 32 bytes, freertos_tcp_transmission.o(i.prvTCPSendRepeated))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = prvTCPSendRepeated &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendPacket
</UL>

<P><STRONG><a name="[162]"></a>prvTCPSendReset</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, freertos_tcp_transmission.o(i.prvTCPSendReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = prvTCPSendReset &rArr; prvTCPSendSpecialPacketHelper &rArr; prvTCPReturnPacket &rArr; xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendSpecialPacketHelper
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvStoreRxData
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
</UL>

<P><STRONG><a name="[1f0]"></a>prvTCPSocketIsActive</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, freertos_tcp_state_handling.o(i.prvTCPSocketIsActive))
<BR><BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
</UL>

<P><STRONG><a name="[1b2]"></a>prvTCPStatusAgeCheck</STRONG> (Thumb, 138 bytes, Stack size 32 bytes, freertos_tcp_state_handling.o(i.prvTCPStatusAgeCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = prvTCPStatusAgeCheck &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_GetTCPStateName
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
</UL>
<BR>[Called By]<UL><LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPSocketCheck
</UL>

<P><STRONG><a name="[f1]"></a>pvPortMalloc</STRONG> (Thumb, 208 bytes, Stack size 32 bytes, heap_4.o(i.pvPortMalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHeapInit
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupCreate
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateStream
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCreateSectors
</UL>

<P><STRONG><a name="[b2]"></a>pxDuplicateNetworkBufferWithDescriptor</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, freertos_ip_utils.o(i.pxDuplicateNetworkBufferWithDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = pxDuplicateNetworkBufferWithDescriptor &rArr; pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_TreatNBNS
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
</UL>

<P><STRONG><a name="[c9]"></a>pxGetNetworkBufferWithDescriptor</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, bufferallocation_2.o(i.pxGetNetworkBufferWithDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxDuplicateNetworkBufferWithDescriptor
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rece_data_task
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPBufferResize
</UL>

<P><STRONG><a name="[171]"></a>pxPortInitialiseStack</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, port.o(i.pxPortInitialiseStack))
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[1ff]"></a>pxTCPSocketLookup</STRONG> (Thumb, 88 bytes, Stack size 28 bytes, freertos_sockets.o(i.pxTCPSocketLookup))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = pxTCPSocketLookup
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
</UL>

<P><STRONG><a name="[b1]"></a>pxUDPPayloadBuffer_to_NetworkBuffer</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, freertos_ip_utils.o(i.pxUDPPayloadBuffer_to_NetworkBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pxUDPPayloadBuffer_to_NetworkBuffer &rArr; prvPacketBuffer_to_NetworkBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvPacketBuffer_to_NetworkBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_TreatNBNS
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
</UL>

<P><STRONG><a name="[1ca]"></a>pxUDPSocketLookup</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, freertos_sockets.o(i.pxUDPSocketLookup))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = pxUDPSocketLookup &rArr; pxListFindListItemWithValue
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxListFindListItemWithValue
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
</UL>

<P><STRONG><a name="[11f]"></a>rcu_ckout0_config</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd32f4xx_rcu.o(i.rcu_ckout0_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_ckout0_config
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
</UL>

<P><STRONG><a name="[127]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_config
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[fc]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_config
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
</UL>

<P><STRONG><a name="[118]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_deinit
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[117]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_deinit
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[6d]"></a>start_task</STRONG> (Thumb, 128 bytes, Stack size 8 bytes, main.o(i.start_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 152 + Unknown Stack Size
<LI>Call Chain = start_task &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_write_read
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[120]"></a>syscfg_enet_phy_interface_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
</UL>

<P><STRONG><a name="[6f]"></a>tcp_server_task</STRONG> (Thumb, 232 bytes, Stack size 152 bytes, main.o(i.tcp_server_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 472 + Unknown Stack Size
<LI>Call Chain = tcp_server_task &rArr; FreeRTOS_send &rArr; prvTCPSendCheck &rArr; prvTCPCreateStream &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_closesocket
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_bind
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_listen
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_accept
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> networkinterface.o(i.vApplicationIPNetworkEventHook)
</UL>
<P><STRONG><a name="[1cd]"></a>trng_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_trng.o(i.trng_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = trng_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_config
</UL>

<P><STRONG><a name="[1ce]"></a>trng_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_config
</UL>

<P><STRONG><a name="[1d2]"></a>trng_flag_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_ready_check
</UL>

<P><STRONG><a name="[1d1]"></a>trng_get_true_random_data</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_get_true_random_data))
<BR><BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_random_range_get
</UL>

<P><STRONG><a name="[125]"></a>trng_init</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, trng.o(i.trng_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = trng_init &rArr; trng_config &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_config
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_inti_task
</UL>

<P><STRONG><a name="[1d0]"></a>trng_random_range_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, trng.o(i.trng_random_range_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = trng_random_range_get &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_get_true_random_data
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_ready_check
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxRand
</UL>

<P><STRONG><a name="[1cf]"></a>trng_ready_check</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, trng.o(i.trng_ready_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_flag_get
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_random_range_get
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_config
</UL>

<P><STRONG><a name="[13c]"></a>uart0_init</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, uart0.o(i.uart0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = uart0_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[161]"></a>ulApplicationGetNextSequenceNumber</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, networkinterface.o(i.ulApplicationGetNextSequenceNumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = ulApplicationGetNextSequenceNumber &rArr; uxRand &rArr; trng_random_range_get &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxRand
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareConnect
</UL>

<P><STRONG><a name="[197]"></a>ulChar2u32</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, freertos_ip_utils.o(i.ulChar2u32))
<BR><BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReadSackOption
</UL>

<P><STRONG><a name="[1d8]"></a>ulDNSHandlePacket</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, freertos_dns.o(i.ulDNSHandlePacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 332<LI>Call Chain = ulDNSHandlePacket &rArr; DNS_ParseDNSReply &rArr; parseDNSAnswer &rArr; FreeRTOS_dns_update &rArr; FreeRTOS_ProcessDNSCache &rArr; prvInsertCacheEntry &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
</UL>

<P><STRONG><a name="[1d9]"></a>ulNBNSHandlePacket</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, freertos_dns.o(i.ulNBNSHandlePacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = ulNBNSHandlePacket &rArr; DNS_TreatNBNS &rArr; pxDuplicateNetworkBufferWithDescriptor &rArr; pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_TreatNBNS
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
</UL>

<P><STRONG><a name="[158]"></a>ulTCPWindowTxAck</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, freertos_tcp_win.o(i.ulTCPWindowTxAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = ulTCPWindowTxAck &rArr; prvTCPWindowTxCheckAck &rArr; prvTCPWindowTxCheckAck_CalcSRTT &rArr; ulTimerGetAge
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEstablished
</UL>

<P><STRONG><a name="[1ab]"></a>ulTCPWindowTxGet</STRONG> (Thumb, 208 bytes, Stack size 40 bytes, freertos_tcp_win.o(i.ulTCPWindowTxGet))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = ulTCPWindowTxGet &rArr; pxTCPWindowTx_GetTXQueue &rArr; prvTCPWindowTxHasSpace
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowGetHead
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPTimerSet
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertFifo
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetWaitQueue
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetTXQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
</UL>

<P><STRONG><a name="[198]"></a>ulTCPWindowTxSack</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, freertos_tcp_win.o(i.ulTCPWindowTxSack))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = ulTCPWindowTxSack &rArr; prvTCPWindowTxCheckAck &rArr; prvTCPWindowTxCheckAck_CalcSRTT &rArr; ulTimerGetAge
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSequenceGreaterThan
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowFastRetransmit
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReadSackOption
</UL>

<P><STRONG><a name="[12b]"></a>ulTaskGenericNotifyTake</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, tasks.o(i.ulTaskGenericNotifyTake))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ulTaskGenericNotifyTake &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rece_data_task
</UL>

<P><STRONG><a name="[ae]"></a>usChar2u16</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, freertos_ip_utils.o(i.usChar2u16))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDNSAnswer
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_TreatNBNS
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSingleStepTCPHeaderOptions
</UL>

<P><STRONG><a name="[13e]"></a>usGenerateChecksum</STRONG> (Thumb, 394 bytes, Stack size 44 bytes, freertos_ip_utils.o(i.usGenerateChecksum))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usGenerateChecksum
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessGeneratedUDPPacket
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessICMPEchoRequest
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateProtocolChecksum
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prepareReplyDNSMessage
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
</UL>

<P><STRONG><a name="[13f]"></a>usGenerateProtocolChecksum</STRONG> (Thumb, 638 bytes, Stack size 72 bytes, freertos_ip_utils.o(i.usGenerateProtocolChecksum))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usGenerateProtocolChecksum &rArr; usGenerateChecksum
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateChecksum
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessGeneratedUDPPacket
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessICMPEchoRequest
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prepareReplyDNSMessage
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
</UL>

<P><STRONG><a name="[1d4]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_init
</UL>

<P><STRONG><a name="[12e]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[1d3]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_init
</UL>

<P><STRONG><a name="[1d6]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_init
</UL>

<P><STRONG><a name="[12f]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[1d5]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart0_init
</UL>

<P><STRONG><a name="[1ed]"></a>uxGetNumberOfFreeNetworkBuffers</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, bufferallocation_2.o(i.uxGetNumberOfFreeNetworkBuffers))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
</UL>

<P><STRONG><a name="[141]"></a>uxListRemove</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, list.o(i.uxListRemove))
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowNew
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowGetHead
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowFree
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowFastRetransmit
</UL>

<P><STRONG><a name="[1db]"></a>uxQueueMessagesWaiting</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, queue.o(i.uxQueueMessagesWaiting))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = uxQueueMessagesWaiting
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
</UL>

<P><STRONG><a name="[1d7]"></a>uxRand</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, networkinterface.o(i.uxRand))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = uxRand &rArr; trng_random_range_get &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_random_range_get
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xApplicationGetRandomNumber
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulApplicationGetNextSequenceNumber
</UL>

<P><STRONG><a name="[ee]"></a>uxStreamBufferAdd</STRONG> (Thumb, 162 bytes, Stack size 40 bytes, freertos_stream_buffer.o(i.uxStreamBufferAdd))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = uxStreamBufferAdd &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xStreamBufferLessThenEqual
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSpace
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_size_t
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPAddRxdata
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
</UL>

<P><STRONG><a name="[1ac]"></a>uxStreamBufferDistance</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, freertos_stream_buffer.o(i.uxStreamBufferDistance))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = uxStreamBufferDistance
</UL>
<BR>[Called By]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferMidSpace
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSize
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
</UL>

<P><STRONG><a name="[e7]"></a>uxStreamBufferFrontSpace</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, freertos_stream_buffer.o(i.uxStreamBufferFrontSpace))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uxStreamBufferFrontSpace &rArr; uxStreamBufferSpace
</UL>
<BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPAddRxdata
</UL>

<P><STRONG><a name="[e6]"></a>uxStreamBufferGet</STRONG> (Thumb, 140 bytes, Stack size 40 bytes, freertos_stream_buffer.o(i.uxStreamBufferGet))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = uxStreamBufferGet &rArr; uxStreamBufferGetSize &rArr; uxStreamBufferDistance
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSize
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_size_t
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEstablished
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReadSackOption
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
</UL>

<P><STRONG><a name="[e9]"></a>uxStreamBufferGetPtr</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, freertos_stream_buffer.o(i.uxStreamBufferGetPtr))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = uxStreamBufferGetPtr &rArr; uxStreamBufferGetSize &rArr; uxStreamBufferDistance
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSize
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_size_t
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
</UL>

<P><STRONG><a name="[e5]"></a>uxStreamBufferGetSize</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, freertos_stream_buffer.o(i.uxStreamBufferGetSize))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uxStreamBufferGetSize &rArr; uxStreamBufferDistance
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferDistance
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetPtr
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGet
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_rx_size
</UL>

<P><STRONG><a name="[ed]"></a>uxStreamBufferGetSpace</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, freertos_stream_buffer.o(i.uxStreamBufferGetSpace))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uxStreamBufferGetSpace &rArr; uxStreamBufferSpace
</UL>
<BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvStoreRxData
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferAdd
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_tx_space
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
</UL>

<P><STRONG><a name="[1a1]"></a>uxStreamBufferMidSpace</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, freertos_stream_buffer.o(i.uxStreamBufferMidSpace))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uxStreamBufferMidSpace &rArr; uxStreamBufferDistance
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferDistance
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPAddTxData
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vStreamBufferMoveMid
</UL>

<P><STRONG><a name="[1de]"></a>uxStreamBufferSpace</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, freertos_stream_buffer.o(i.uxStreamBufferSpace))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = uxStreamBufferSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGetSpace
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferFrontSpace
</UL>

<P><STRONG><a name="[178]"></a>uxTaskGetNumberOfTasks</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.uxTaskGetNumberOfTasks))
<BR><BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
</UL>

<P><STRONG><a name="[1f7]"></a>uxTaskResetEventItemValue</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, tasks.o(i.uxTaskResetEventItemValue))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
</UL>

<P><STRONG><a name="[181]"></a>vARPAgeCache</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, freertos_arp.o(i.vARPAgeCache))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = vARPAgeCache &rArr; FreeRTOS_OutputARPRequest &rArr; pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[ca]"></a>vARPGenerateRequestPacket</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, freertos_arp.o(i.vARPGenerateRequestPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vARPGenerateRequestPacket
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessGeneratedUDPPacket
</UL>

<P><STRONG><a name="[114]"></a>vARPRefreshCacheEntry</STRONG> (Thumb, 342 bytes, Stack size 40 bytes, freertos_arp.o(i.vARPRefreshCacheEntry))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vARPRefreshCacheEntry &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPProcessPacket
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessARPPacketReply
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessGeneratedUDPPacket
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPPacket
</UL>

<P><STRONG><a name="[1df]"></a>vARPTimerReload</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, freertos_ip_timers.o(i.vARPTimerReload))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vARPTimerReload &rArr; prvIPTimerReload &rArr; prvIPTimerStart &rArr; vTaskSetTimeOutState
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerReload
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPNetworkUpCalls
</UL>

<P><STRONG><a name="[18f]"></a>vApplicationIPNetworkEventHook</STRONG> (Thumb, 210 bytes, Stack size 48 bytes, networkinterface.o(i.vApplicationIPNetworkEventHook))
<BR><BR>[Stack]<UL><LI>Max Depth = 208 + Unknown Stack Size
<LI>Call Chain = vApplicationIPNetworkEventHook &rArr; ethernet_task_creation &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_GetAddressConfiguration
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_inet_ntoa
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_task_creation
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessNetworkDownEvent
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPNetworkUpCalls
</UL>

<P><STRONG><a name="[17d]"></a>vCheckNetworkTimers</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, freertos_ip_timers.o(i.vCheckNetworkTimers))
<BR><BR>[Stack]<UL><LI>Max Depth = 448 + Unknown Stack Size
<LI>Call Chain = vCheckNetworkTimers &rArr; xTCPTimerCheck &rArr; xTCPSocketCheck &rArr; prvTCPSendPacket &rArr; prvTCPSendRepeated &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxQueueMessagesWaiting
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventToIPTask
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPSetARPResolutionTimerEnableState
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPTimerCheck
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketListenNextTime
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketCloseNextTime
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerStart
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[189]"></a>vEventGroupDelete</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, event_groups.o(i.vEventGroupDelete))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = vEventGroupDelete &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskRemoveFromUnorderedEventList
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[191]"></a>vIPNetworkUpCalls</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, freertos_ip.o(i.vIPNetworkUpCalls))
<BR><BR>[Stack]<UL><LI>Max Depth = 216 + Unknown Stack Size
<LI>Call Chain = vIPNetworkUpCalls &rArr; vApplicationIPNetworkEventHook &rArr; ethernet_task_creation &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationIPNetworkEventHook
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPTimerReload
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessNetworkDownEvent
</UL>

<P><STRONG><a name="[169]"></a>vIPSetARPResolutionTimerEnableState</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, freertos_ip_timers.o(i.vIPSetARPResolutionTimerEnableState))
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessARPPacketReply
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTask
</UL>

<P><STRONG><a name="[18e]"></a>vIPSetARPTimerEnableState</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, freertos_ip_timers.o(i.vIPSetARPTimerEnableState))
<BR><BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessNetworkDownEvent
</UL>

<P><STRONG><a name="[187]"></a>vIPSetTCPTimerExpiredState</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, freertos_ip_timers.o(i.vIPSetTCPTimerExpiredState))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[17a]"></a>vIPTimerStartARPResolution</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, freertos_ip_timers.o(i.vIPTimerStartARPResolution))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = vIPTimerStartARPResolution &rArr; prvIPTimerStart &rArr; vTaskSetTimeOutState
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerStart
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessEthernetPacket
</UL>

<P><STRONG><a name="[f4]"></a>vListInitialise</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, list.o(i.vListInitialise))
<BR><BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupCreate
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkBuffersInitialise
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vNetworkSocketsInit
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCreateSectors
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowCreate
</UL>

<P><STRONG><a name="[f5]"></a>vListInitialiseItem</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, list.o(i.vListInitialiseItem))
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkBuffersInitialise
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
</UL>

<P><STRONG><a name="[142]"></a>vListInsert</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, list.o(i.vListInsert))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkBuffersInitialise
</UL>

<P><STRONG><a name="[1ea]"></a>vListInsertEnd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, list.o(i.vListInsertEnd))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketBind
</UL>

<P><STRONG><a name="[c4]"></a>vLoggingPrintf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, networkinterface.o(i.vLoggingPrintf))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPCheckNewClient
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketBind
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTask
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateProtocolChecksum
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_closesocket
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_bind
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_ProcessDNSCache
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSocketCopy
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleFin
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleSynReceived
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEstablished
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxSack
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowRxCheck
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvStoreRxData
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSingleStepTCPHeaderOptions
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPStatusAgeCheck
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendPacket
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPNextTimeout
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPSocketCheck
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPAddRxdata
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSetSocketCount
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateStream
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowRxConfirm
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowNew
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetWaitQueue
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetTXQueue
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxAdd_FrontSegment
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowRx_UnexpectedRX
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowRx_ExpectedRX
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowFastRetransmit
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCreateSectors
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowCreate
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxGet
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowTxAdd
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvWinScaleFactor
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareConnect
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowRxEmpty
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateWindow
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSocketSetMSS
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetOptions
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSendData
</UL>

<P><STRONG><a name="[c2]"></a>vNetworkSocketsInit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, freertos_sockets.o(i.vNetworkSocketsInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vNetworkSocketsInit
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
</UL>

<P><STRONG><a name="[144]"></a>vPortEnterCritical</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, port.o(i.vPortEnterCritical))
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSetTimeOutState
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxQueueMessagesWaiting
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupClearBits
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskGenericNotifyTake
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
</UL>

<P><STRONG><a name="[146]"></a>vPortExitCritical</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, port.o(i.vPortExitCritical))
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSetTimeOutState
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxQueueMessagesWaiting
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupClearBits
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskGenericNotifyTake
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
</UL>

<P><STRONG><a name="[f3]"></a>vPortFree</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, heap_4.o(i.vPortFree))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBuffer
</UL>

<P><STRONG><a name="[1fc]"></a>vPortSetupTimerInterrupt</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(i.vPortSetupTimerInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[be]"></a>vPreCheckConfigs</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, freertos_ip_utils.o(i.vPreCheckConfigs))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
</UL>

<P><STRONG><a name="[185]"></a>vProcessGeneratedUDPPacket</STRONG> (Thumb, 242 bytes, Stack size 40 bytes, freertos_udp_ip.o(i.vProcessGeneratedUDPPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = vProcessGeneratedUDPPacket &rArr; xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceOutput
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPRefreshCacheEntry
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPGenerateRequestPacket
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPGetCacheEntry
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateProtocolChecksum
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateChecksum
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[c0]"></a>vQueueAddToRegistry</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, queue.o(i.vQueueAddToRegistry))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkBuffersInitialise
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
</UL>

<P><STRONG><a name="[c5]"></a>vQueueDelete</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, queue.o(i.vQueueDelete))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = vQueueDelete &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueUnregisterQueue
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
</UL>

<P><STRONG><a name="[1e7]"></a>vQueueUnregisterQueue</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, queue.o(i.vQueueUnregisterQueue))
<BR><BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueDelete
</UL>

<P><STRONG><a name="[195]"></a>vQueueWaitForMessageRestricted</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, queue.o(i.vQueueWaitForMessageRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = vQueueWaitForMessageRestricted &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[1e9]"></a>vReleaseNetworkBuffer</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, bufferallocation_2.o(i.vReleaseNetworkBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = vReleaseNetworkBuffer &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
</UL>

<P><STRONG><a name="[b5]"></a>vReleaseNetworkBufferAndDescriptor</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, bufferallocation_2.o(i.vReleaseNetworkBufferAndDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceOutput
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessARPPacketReply
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessGeneratedUDPPacket
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessEthernetPacket
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_TreatNBNS
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendPacket
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPSocketCheck
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rece_data_task
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPBufferResize
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSendData
</UL>

<P><STRONG><a name="[b4]"></a>vReturnEthernetFrame</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, freertos_ip.o(i.vReturnEthernetFrame))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = vReturnEthernetFrame &rArr; xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessEthernetPacket
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_TreatNBNS
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
</UL>

<P><STRONG><a name="[110]"></a>vSetMultiCastIPv4MacAddress</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, freertos_ip_utils.o(i.vSetMultiCastIPv4MacAddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vSetMultiCastIPv4MacAddress
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPGetCacheEntry
</UL>

<P><STRONG><a name="[182]"></a>vSocketBind</STRONG> (Thumb, 166 bytes, Stack size 40 bytes, freertos_sockets.o(i.vSocketBind))
<BR><BR>[Stack]<UL><LI>Max Depth = 256 + Unknown Stack Size
<LI>Call Chain = vSocketBind &rArr; prvGetPrivatePortNumber &rArr; xApplicationGetRandomNumber &rArr; uxRand &rArr; trng_random_range_get &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxListFindListItemWithValue
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetPrivatePortNumber
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSocketCopy
</UL>

<P><STRONG><a name="[184]"></a>vSocketClose</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, freertos_sockets.o(i.vSocketClose))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowDestroy
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxGetNumberOfFreeNetworkBuffers
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSetSocketCount
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketCloseNextTime
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSocketCopy
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSetSocketCount
</UL>

<P><STRONG><a name="[1e2]"></a>vSocketCloseNextTime</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, freertos_tcp_ip.o(i.vSocketCloseNextTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
</UL>

<P><STRONG><a name="[1e3]"></a>vSocketListenNextTime</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, freertos_tcp_ip.o(i.vSocketListenNextTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = vSocketListenNextTime &rArr; FreeRTOS_listen &rArr;  vTCPStateChange (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_listen
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
</UL>

<P><STRONG><a name="[186]"></a>vSocketSelect</STRONG> (Thumb, 388 bytes, Stack size 48 bytes, freertos_sockets.o(i.vSocketSelect))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = vSocketSelect &rArr; xEventGroupSetBits &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupClearBits
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_tx_space
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_rx_size
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[183]"></a>vSocketWakeUpUser</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, freertos_sockets.o(i.vSocketWakeUpUser))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = vSocketWakeUpUser &rArr; xEventGroupSetBits &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPTimerCheck
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
</UL>

<P><STRONG><a name="[e1]"></a>vStreamBufferClear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, freertos_stream_buffer.o(i.vStreamBufferClear))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_listen
</UL>

<P><STRONG><a name="[1a2]"></a>vStreamBufferMoveMid</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, freertos_stream_buffer.o(i.vStreamBufferMoveMid))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vStreamBufferMoveMid &rArr; uxStreamBufferMidSpace &rArr; uxStreamBufferDistance
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferMidSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPAddTxData
</UL>

<P><STRONG><a name="[e3]"></a>vTCPStateChange</STRONG> (Thumb, 438 bytes, Stack size 48 bytes, freertos_tcp_ip.o(i.vTCPStateChange))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketListenNextTime
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketCloseNextTime
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketWakeUpUser
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSocketIsActive
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_GetTCPStateName
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPTouchSocket
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleFin
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleSynReceived
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPStatusAgeCheck
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendPacket
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_listen
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateStream
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
</UL>

<P><STRONG><a name="[168]"></a>vTCPTimerReload</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, freertos_ip_timers.o(i.vTCPTimerReload))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vTCPTimerReload &rArr; prvIPTimerReload &rArr; prvIPTimerStart &rArr; vTaskSetTimeOutState
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerReload
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTask
</UL>

<P><STRONG><a name="[1a4]"></a>vTCPWindowCreate</STRONG> (Thumb, 102 bytes, Stack size 32 bytes, freertos_tcp_win.o(i.vTCPWindowCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = vTCPWindowCreate &rArr; prvCreateSectors &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCreateSectors
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowInit
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateWindow
</UL>

<P><STRONG><a name="[1ec]"></a>vTCPWindowDestroy</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, freertos_tcp_win.o(i.vTCPWindowDestroy))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vTCPWindowDestroy &rArr; vTCPWindowFree &rArr; vListInsertFifo
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowFree
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
</UL>

<P><STRONG><a name="[167]"></a>vTCPWindowInit</STRONG> (Thumb, 84 bytes, Stack size 12 bytes, freertos_tcp_win.o(i.vTCPWindowInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = vTCPWindowInit
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleSynReceived
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowCreate
</UL>

<P><STRONG><a name="[10d]"></a>vTaskDelay</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, tasks.o(i.vTaskDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessNetworkDownEvent
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_task
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clock_test_task
</UL>

<P><STRONG><a name="[126]"></a>vTaskDelete</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, tasks.o(i.vTaskDelete))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = vTaskDelete &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_inti_task
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tcp_server_task
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;http_server_task
</UL>

<P><STRONG><a name="[b9]"></a>vTaskGenericNotifyGiveFromISR</STRONG> (Thumb, 310 bytes, Stack size 24 bytes, tasks.o(i.vTaskGenericNotifyGiveFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vTaskGenericNotifyGiveFromISR
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ENET_IRQHandler
</UL>

<P><STRONG><a name="[200]"></a>vTaskInternalSetTimeOutState</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, tasks.o(i.vTaskInternalSetTimeOutState))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[1c2]"></a>vTaskMissedYield</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskMissedYield))
<BR><BR>[Called By]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[1f2]"></a>vTaskPlaceOnEventList</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[1e8]"></a>vTaskPlaceOnEventListRestricted</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnEventListRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[1f3]"></a>vTaskPlaceOnUnorderedEventList</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnUnorderedEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = vTaskPlaceOnUnorderedEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
</UL>

<P><STRONG><a name="[1e4]"></a>vTaskRemoveFromUnorderedEventList</STRONG> (Thumb, 190 bytes, Stack size 16 bytes, tasks.o(i.vTaskRemoveFromUnorderedEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vTaskRemoveFromUnorderedEventList
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
</UL>

<P><STRONG><a name="[d7]"></a>vTaskSetTimeOutState</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, tasks.o(i.vTaskSetTimeOutState))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vTaskSetTimeOutState
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPProcessPacket
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerStart
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_accept
</UL>

<P><STRONG><a name="[13d]"></a>vTaskStartScheduler</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, tasks.o(i.vTaskStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d5]"></a>vTaskSuspendAll</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tasks.o(i.vTaskSuspendAll))
<BR><BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferAdd
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_accept
</UL>

<P><STRONG><a name="[8f]"></a>vTaskSwitchContext</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, tasks.o(i.vTaskSwitchContext))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>

<P><STRONG><a name="[b0]"></a>xApplicationDNSQueryHook</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, networkinterface.o(i.xApplicationDNSQueryHook))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_TreatNBNS
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DNS_ParseDNSReply
</UL>

<P><STRONG><a name="[155]"></a>xApplicationGetRandomNumber</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, networkinterface.o(i.xApplicationGetRandomNumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = xApplicationGetRandomNumber &rArr; uxRand &rArr; trng_random_range_get &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxRand
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetPrivatePortNumber
</UL>

<P><STRONG><a name="[17e]"></a>xCalculateSleepTime</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, freertos_ip_timers.o(i.xCalculateSleepTime))
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[18b]"></a>xCheckRequiresARPResolution</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, freertos_arp.o(i.xCheckRequiresARPResolution))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = xCheckRequiresARPResolution &rArr; FreeRTOS_OutputARPRequest &rArr; pxGetNetworkBufferWithDescriptor &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIsIPInARPCache
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPPacket
</UL>

<P><STRONG><a name="[1ee]"></a>xEventGroupClearBits</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, event_groups.o(i.xEventGroupClearBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xEventGroupClearBits
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketSelect
</UL>

<P><STRONG><a name="[f2]"></a>xEventGroupCreate</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, event_groups.o(i.xEventGroupCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = xEventGroupCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
</UL>

<P><STRONG><a name="[1ef]"></a>xEventGroupSetBits</STRONG> (Thumb, 146 bytes, Stack size 48 bytes, event_groups.o(i.xEventGroupSetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = xEventGroupSetBits &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskRemoveFromUnorderedEventList
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketWakeUpUser
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketSelect
</UL>

<P><STRONG><a name="[d9]"></a>xEventGroupWaitBits</STRONG> (Thumb, 198 bytes, Stack size 56 bytes, event_groups.o(i.xEventGroupWaitBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xEventGroupWaitBits &rArr; vTaskPlaceOnUnorderedEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnUnorderedEventList
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxTaskResetEventItemValue
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTestWaitCondition
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_bind
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_accept
</UL>

<P><STRONG><a name="[152]"></a>xIPIsNetworkTaskReady</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, freertos_ip.o(i.xIPIsNetworkTaskReady))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxListFindListItemWithValue
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDetermineSocketSize
</UL>

<P><STRONG><a name="[cb]"></a>xIsCallingFromIPTask</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, freertos_ip_utils.o(i.xIsCallingFromIPTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xIsCallingFromIPTask
</UL>
<BR>[Calls]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetCurrentTaskHandle
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_GetIPTaskHandle
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
</UL>

<P><STRONG><a name="[1e6]"></a>xIsIPInARPCache</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, freertos_arp.o(i.xIsIPInARPCache))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = xIsIPInARPCache
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xCheckRequiresARPResolution
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessARPPacketReply
</UL>

<P><STRONG><a name="[10f]"></a>xIsIPv4Multicast</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, freertos_ip.o(i.xIsIPv4Multicast))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = xIsIPv4Multicast
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPGetCacheEntry
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAllowIPPacket
</UL>

<P><STRONG><a name="[c1]"></a>xNetworkBuffersInitialise</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, bufferallocation_2.o(i.xNetworkBuffersInitialise))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = xNetworkBuffersInitialise &rArr; xQueueCreateCountingSemaphore &rArr; xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateCountingSemaphore
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
</UL>

<P><STRONG><a name="[190]"></a>xNetworkInterfaceInitialise</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, networkinterface.o(i.xNetworkInterfaceInitialise))
<BR><BR>[Stack]<UL><LI>Max Depth = 248 + Unknown Stack Size
<LI>Call Chain = xNetworkInterfaceInitialise &rArr; InitialiseNetwork &rArr; enet_gpio_config &rArr; check_rmii_pins &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessNetworkDownEvent
</UL>

<P><STRONG><a name="[cc]"></a>xNetworkInterfaceOutput</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, networkinterface.o(i.xNetworkInterfaceOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_frame_transmit
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessGeneratedUDPPacket
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReturnEthernetFrame
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
</UL>

<P><STRONG><a name="[1f5]"></a>xPortStartScheduler</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, port.o(i.xPortStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = xPortStartScheduler
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSetupTimerInterrupt
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvEnableVFP
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvStartFirstTask
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[18d]"></a>xProcessReceivedTCPPacket</STRONG> (Thumb, 648 bytes, Stack size 64 bytes, freertos_tcp_ip.o(i.xProcessReceivedTCPPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 520 + Unknown Stack Size
<LI>Call Chain = xProcessReceivedTCPPacket &rArr; prvTCPHandleState &rArr; prvHandleEstablished &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSequenceLessThan
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSequenceGreaterThan
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSocketIsActive
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendReset
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendRepeated
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendChallengeAck
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPFlagMeaning
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckOptions
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPTouchSocket
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPNextTimeout
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPSocketLookup
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPPacket
</UL>

<P><STRONG><a name="[18c]"></a>xProcessReceivedUDPPacket</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, freertos_udp_ip.o(i.xProcessReceivedUDPPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 364<LI>Call Chain = xProcessReceivedUDPPacket &rArr; ulDNSHandlePacket &rArr; DNS_ParseDNSReply &rArr; parseDNSAnswer &rArr; FreeRTOS_dns_update &rArr; FreeRTOS_ProcessDNSCache &rArr; prvInsertCacheEntry &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xCheckRequiresARPResolution
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPRefreshCacheEntry
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulNBNSHandlePacket
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulDNSHandlePacket
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxUDPSocketLookup
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPPacket
</UL>

<P><STRONG><a name="[1fa]"></a>xQueueCreateCountingSemaphore</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, queue.o(i.xQueueCreateCountingSemaphore))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = xQueueCreateCountingSemaphore &rArr; xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkBuffersInitialise
</UL>

<P><STRONG><a name="[bf]"></a>xQueueGenericCreate</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, queue.o(i.xQueueGenericCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueCreateCountingSemaphore
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_task_creation
</UL>

<P><STRONG><a name="[16f]"></a>xQueueGenericReset</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, queue.o(i.xQueueGenericReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>

<P><STRONG><a name="[1eb]"></a>xQueueGenericSend</STRONG> (Thumb, 322 bytes, Stack size 56 bytes, queue.o(i.xQueueGenericSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
</UL>

<P><STRONG><a name="[17f]"></a>xQueueReceive</STRONG> (Thumb, 256 bytes, Stack size 48 bytes, queue.o(i.xQueueReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = xQueueReceive &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[1c5]"></a>xQueueSemaphoreTake</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, queue.o(i.xQueueSemaphoreTake))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xQueueSemaphoreTake &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxGetNetworkBufferWithDescriptor
</UL>

<P><STRONG><a name="[c7]"></a>xSendEventStructToIPTask</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, freertos_ip.o(i.xSendEventStructToIPTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = xSendEventStructToIPTask &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxQueueMessagesWaiting
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIsCallingFromIPTask
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPSetTCPTimerExpiredState
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIPIsNetworkTaskReady
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventToIPTask
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessARPPacketReply
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_NetworkDown
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_closesocket
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_bind
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_accept
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rece_data_task
</UL>

<P><STRONG><a name="[e8]"></a>xSendEventToIPTask</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, freertos_ip.o(i.xSendEventToIPTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = xSendEventToIPTask &rArr; xSendEventStructToIPTask &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPAddRxdata
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
</UL>

<P><STRONG><a name="[1bb]"></a>xSequenceGreaterThan</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, freertos_tcp_win.o(i.xSequenceGreaterThan))
<BR><BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxSack
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck
</UL>

<P><STRONG><a name="[1b6]"></a>xSequenceLessThan</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, freertos_tcp_win.o(i.xSequenceLessThan))
<BR><BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowRxConfirm
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowFastRetransmit
</UL>

<P><STRONG><a name="[1dd]"></a>xStreamBufferLessThenEqual</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, freertos_stream_buffer.o(i.xStreamBufferLessThenEqual))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xStreamBufferLessThenEqual
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferAdd
</UL>

<P><STRONG><a name="[188]"></a>xTCPCheckNewClient</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, freertos_tcp_ip.o(i.xTCPCheckNewClient))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xTCPCheckNewClient
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[201]"></a>xTCPSocketCheck</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, freertos_tcp_ip.o(i.xTCPSocketCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 392 + Unknown Stack Size
<LI>Call Chain = xTCPSocketCheck &rArr; prvTCPSendPacket &rArr; prvTCPSendRepeated &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPStatusAgeCheck
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendPacket
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPAddTxData
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPNextTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPTimerCheck
</UL>

<P><STRONG><a name="[1e1]"></a>xTCPTimerCheck</STRONG> (Thumb, 154 bytes, Stack size 40 bytes, freertos_sockets.o(i.xTCPTimerCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 432 + Unknown Stack Size
<LI>Call Chain = xTCPTimerCheck &rArr; xTCPSocketCheck &rArr; prvTCPSendPacket &rArr; prvTCPSendRepeated &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketWakeUpUser
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPSocketCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
</UL>

<P><STRONG><a name="[15a]"></a>xTCPWindowRxEmpty</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, freertos_tcp_win.o(i.xTCPWindowRxEmpty))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTCPWindowRxEmpty
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSequenceGreaterThanOrEqual
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEstablished
</UL>

<P><STRONG><a name="[15b]"></a>xTCPWindowTxDone</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, freertos_tcp_win.o(i.xTCPWindowTxDone))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEstablished
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
</UL>

<P><STRONG><a name="[1aa]"></a>xTCPWindowTxHasData</STRONG> (Thumb, 144 bytes, Stack size 40 bytes, freertos_tcp_win.o(i.xTCPWindowTxHasData))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = xTCPWindowTxHasData &rArr; prvTCPWindowTxHasSpace
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowPeekHead
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTimerGetAge
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxHasSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPNextTimeout
</UL>

<P><STRONG><a name="[d8]"></a>xTaskCheckForTimeOut</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, tasks.o(i.xTaskCheckForTimeOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskCheckForTimeOut
</UL>
<BR>[Calls]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPProcessPacket
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerCheck
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_accept
</UL>

<P><STRONG><a name="[c3]"></a>xTaskCreate</STRONG> (Thumb, 104 bytes, Stack size 72 bytes, tasks.o(i.xTaskCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationIPNetworkEventHook
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_IPInit
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ethernet_task_creation
</UL>

<P><STRONG><a name="[1f8]"></a>xTaskGetCurrentTaskHandle</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetCurrentTaskHandle))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIsCallingFromIPTask
</UL>

<P><STRONG><a name="[ce]"></a>xTaskGetTickCount</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetTickCount))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPAgeCache
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPProcessPacket
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPTimerCheck
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_ProcessDNSCache
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPStatusAgeCheck
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPTouchSocket
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPTimerSet
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTimerGetAge
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
</UL>

<P><STRONG><a name="[109]"></a>xTaskIncrementTick</STRONG> (Thumb, 356 bytes, Stack size 24 bytes, tasks.o(i.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[177]"></a>xTaskRemoveFromEventList</STRONG> (Thumb, 240 bytes, Stack size 16 bytes, tasks.o(i.xTaskRemoveFromEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskRemoveFromEventList
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
</UL>

<P><STRONG><a name="[d6]"></a>xTaskResumeAll</STRONG> (Thumb, 310 bytes, Stack size 16 bytes, tasks.o(i.xTaskResumeAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupSetBits
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferAdd
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_accept
</UL>

<P><STRONG><a name="[1f4]"></a>xTimerCreateTimerTask</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, timers.o(i.xTimerCreateTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = xTimerCreateTimerTask &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[83]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[23b]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[23c]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[1cb]"></a>system_clock_200m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_200m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[10a]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_200m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[124]"></a>enet_default_init</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, gd32f4xx_enet.o(i.enet_default_init))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_init
</UL>

<P><STRONG><a name="[123]"></a>enet_delay</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, gd32f4xx_enet.o(i.enet_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = enet_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_phy_config
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_init
</UL>

<P><STRONG><a name="[1f6]"></a>prvTestWaitCondition</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, event_groups.o(i.prvTestWaitCondition))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvTestWaitCondition
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xEventGroupWaitBits
</UL>

<P><STRONG><a name="[14f]"></a>prvCopyDataFromQueue</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, queue.o(i.prvCopyDataFromQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvCopyDataFromQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[150]"></a>prvCopyDataToQueue</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, queue.o(i.prvCopyDataToQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
</UL>

<P><STRONG><a name="[16e]"></a>prvInitialiseNewQueue</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, queue.o(i.prvInitialiseNewQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[174]"></a>prvIsQueueEmpty</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, queue.o(i.prvIsQueueEmpty))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvIsQueueEmpty
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[175]"></a>prvIsQueueFull</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, queue.o(i.prvIsQueueFull))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvIsQueueFull
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[176]"></a>prvNotifyQueueSetContainer</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, queue.o(i.prvNotifyQueueSetContainer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxTaskGetNumberOfTasks
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[1c1]"></a>prvUnlockQueue</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, queue.o(i.prvUnlockQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskMissedYield
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[140]"></a>prvAddCurrentTaskToDelayedList</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, tasks.o(i.prvAddCurrentTaskToDelayedList))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnUnorderedEventList
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskGenericNotifyTake
</UL>

<P><STRONG><a name="[143]"></a>prvAddNewTaskToReadyList</STRONG> (Thumb, 200 bytes, Stack size 8 bytes, tasks.o(i.prvAddNewTaskToReadyList))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvAddNewTaskToReadyList &rArr; prvInitialiseTaskLists
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[14d]"></a>prvCheckTasksWaitingTermination</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, tasks.o(i.prvCheckTasksWaitingTermination))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[14e]"></a>prvDeleteTCB</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, tasks.o(i.prvDeleteTCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
</UL>

<P><STRONG><a name="[72]"></a>prvIdleTask</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, tasks.o(i.prvIdleTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = prvIdleTask &rArr; prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
</UL>
<BR>[Address Reference Count : 1]<UL><LI> tasks.o(i.vTaskStartScheduler)
</UL>
<P><STRONG><a name="[170]"></a>prvInitialiseNewTask</STRONG> (Thumb, 136 bytes, Stack size 40 bytes, tasks.o(i.prvInitialiseNewTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxPortInitialiseStack
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[145]"></a>prvInitialiseTaskLists</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, tasks.o(i.prvInitialiseTaskLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvInitialiseTaskLists
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[1f1]"></a>prvResetNextTaskUnblockTime</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, tasks.o(i.prvResetNextTaskUnblockTime))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelete
</UL>

<P><STRONG><a name="[149]"></a>prvCheckForValidListAndQueue</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, timers.o(i.prvCheckForValidListAndQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = prvCheckForValidListAndQueue &rArr; xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[1c0]"></a>prvGetNextExpireTime</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, timers.o(i.prvGetNextExpireTime))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[173]"></a>prvInsertTimerInActiveList</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, timers.o(i.prvInsertTimerInActiveList))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReloadTimer
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[17b]"></a>prvProcessExpiredTimer</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, timers.o(i.prvProcessExpiredTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = prvProcessExpiredTimer &rArr; prvReloadTimer &rArr; prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReloadTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[192]"></a>prvProcessReceivedCommands</STRONG> (Thumb, 268 bytes, Stack size 40 bytes, timers.o(i.prvProcessReceivedCommands))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = prvProcessReceivedCommands &rArr; xQueueReceive &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReloadTimer
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[194]"></a>prvProcessTimerOrBlockTask</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, timers.o(i.prvProcessTimerOrBlockTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = prvProcessTimerOrBlockTask &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; prvProcessExpiredTimer &rArr; prvReloadTimer &rArr; prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[17c]"></a>prvReloadTimer</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, timers.o(i.prvReloadTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = prvReloadTimer &rArr; prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>

<P><STRONG><a name="[193]"></a>prvSampleTimeNow</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, timers.o(i.prvSampleTimeNow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; prvProcessExpiredTimer &rArr; prvReloadTimer &rArr; prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[199]"></a>prvSwitchTimerLists</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, timers.o(i.prvSwitchTimerLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = prvSwitchTimerLists &rArr; prvProcessExpiredTimer &rArr; prvReloadTimer &rArr; prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[73]"></a>prvTimerTask</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, timers.o(i.prvTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = prvTimerTask &rArr; prvProcessReceivedCommands &rArr; xQueueReceive &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetNextExpireTime
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(i.xTimerCreateTimerTask)
</UL>
<P><STRONG><a name="[1c3]"></a>prvHeapInit</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, heap_4.o(i.prvHeapInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = prvHeapInit
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[1c4]"></a>prvInsertBlockIntoFreeList</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, heap_4.o(i.prvInsertBlockIntoFreeList))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvInsertBlockIntoFreeList
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[6e]"></a>prvTaskExitError</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, port.o(i.prvTaskExitError))
<BR>[Address Reference Count : 1]<UL><LI> port.o(i.pxPortInitialiseStack)
</UL>
<P><STRONG><a name="[111]"></a>prvCacheLookup</STRONG> (Thumb, 74 bytes, Stack size 12 bytes, freertos_arp.o(i.prvCacheLookup))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = prvCacheLookup
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPGetCacheEntry
</UL>

<P><STRONG><a name="[115]"></a>vProcessARPPacketReply</STRONG> (Thumb, 122 bytes, Stack size 40 bytes, freertos_arp.o(i.vProcessARPPacketReply))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = vProcessARPPacketReply &rArr; xSendEventStructToIPTask &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSendEventStructToIPTask
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPSetARPResolutionTimerEnableState
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIsIPInARPCache
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPRefreshCacheEntry
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPProcessPacket
</UL>

<P><STRONG><a name="[cf]"></a>prvFindEntryIndex</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, freertos_dns_cache.o(i.prvFindEntryIndex))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prvFindEntryIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_ProcessDNSCache
</UL>

<P><STRONG><a name="[d0]"></a>prvGetCacheIPEntry</STRONG> (Thumb, 130 bytes, Stack size 20 bytes, freertos_dns_cache.o(i.prvGetCacheIPEntry))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = prvGetCacheIPEntry
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_ProcessDNSCache
</UL>

<P><STRONG><a name="[d2]"></a>prvInsertCacheEntry</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, freertos_dns_cache.o(i.prvInsertCacheEntry))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = prvInsertCacheEntry &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_ProcessDNSCache
</UL>

<P><STRONG><a name="[d1]"></a>prvUpdateCacheEntry</STRONG> (Thumb, 44 bytes, Stack size 20 bytes, freertos_dns_cache.o(i.prvUpdateCacheEntry))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = prvUpdateCacheEntry
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_ProcessDNSCache
</UL>

<P><STRONG><a name="[108]"></a>prvProcessICMPEchoRequest</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, freertos_icmp.o(i.prvProcessICMPEchoRequest))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = prvProcessICMPEchoRequest &rArr; usGenerateProtocolChecksum &rArr; usGenerateChecksum
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateProtocolChecksum
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usGenerateChecksum
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ProcessICMPPacket
</UL>

<P><STRONG><a name="[147]"></a>prvAllowIPPacket</STRONG> (Thumb, 260 bytes, Stack size 32 bytes, freertos_ip.o(i.prvAllowIPPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = prvAllowIPPacket &rArr; xCheckSizeFields
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIsIPv4Multicast
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xCheckSizeFields
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPPacket
</UL>

<P><STRONG><a name="[15e]"></a>prvHandleEthernetPacket</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, freertos_ip.o(i.prvHandleEthernetPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 600 + Unknown Stack Size
<LI>Call Chain = prvHandleEthernetPacket &rArr; prvProcessEthernetPacket &rArr; prvProcessIPPacket &rArr; xProcessReceivedTCPPacket &rArr; prvTCPHandleState &rArr; prvHandleEstablished &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessEthernetPacket
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
</UL>

<P><STRONG><a name="[6a]"></a>prvIPTask</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, freertos_ip.o(i.prvIPTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 632 + Unknown Stack Size
<LI>Call Chain = prvIPTask &rArr; prvProcessIPEventsAndTimers &rArr; prvHandleEthernetPacket &rArr; prvProcessEthernetPacket &rArr; prvProcessIPPacket &rArr; xProcessReceivedTCPPacket &rArr; prvTCPHandleState &rArr; prvHandleEstablished &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPSetARPResolutionTimerEnableState
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPTimerReload
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_NetworkDown
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPEventsAndTimers
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos_ip.o(i.FreeRTOS_IPInit)
</UL>
<P><STRONG><a name="[15f]"></a>prvProcessEthernetPacket</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, freertos_ip.o(i.prvProcessEthernetPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 592 + Unknown Stack Size
<LI>Call Chain = prvProcessEthernetPacket &rArr; prvProcessIPPacket &rArr; xProcessReceivedTCPPacket &rArr; prvTCPHandleState &rArr; prvHandleEstablished &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReleaseNetworkBufferAndDescriptor
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPProcessPacket
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPTimerStartARPResolution
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessIPPacket
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vReturnEthernetFrame
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEthernetPacket
</UL>

<P><STRONG><a name="[16a]"></a>prvProcessIPEventsAndTimers</STRONG> (Thumb, 254 bytes, Stack size 32 bytes, freertos_ip.o(i.prvProcessIPEventsAndTimers))
<BR><BR>[Stack]<UL><LI>Max Depth = 632 + Unknown Stack Size
<LI>Call Chain = prvProcessIPEventsAndTimers &rArr; prvHandleEthernetPacket &rArr; prvProcessEthernetPacket &rArr; prvProcessIPPacket &rArr; xProcessReceivedTCPPacket &rArr; prvTCPHandleState &rArr; prvHandleEstablished &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vEventGroupDelete
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xNetworkInterfaceOutput
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPAgeCache
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPCheckNewClient
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xCalculateSleepTime
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketWakeUpUser
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketSelect
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketBind
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vProcessGeneratedUDPPacket
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPSetTCPTimerExpiredState
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessNetworkDownEvent
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEthernetPacket
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTask
</UL>

<P><STRONG><a name="[179]"></a>prvProcessIPPacket</STRONG> (Thumb, 412 bytes, Stack size 56 bytes, freertos_ip.o(i.prvProcessIPPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 576 + Unknown Stack Size
<LI>Call Chain = prvProcessIPPacket &rArr; xProcessReceivedTCPPacket &rArr; prvTCPHandleState &rArr; prvHandleEstablished &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xCheckRequiresARPResolution
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPRefreshCacheEntry
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedUDPPacket
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAllowIPPacket
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ProcessICMPPacket
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memmove
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessEthernetPacket
</UL>

<P><STRONG><a name="[148]"></a>xCheckSizeFields</STRONG> (Thumb, 164 bytes, Stack size 28 bytes, freertos_ip.o(i.xCheckSizeFields))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = xCheckSizeFields
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAllowIPPacket
</UL>

<P><STRONG><a name="[16b]"></a>prvIPTimerCheck</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, freertos_ip_timers.o(i.prvIPTimerCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvIPTimerCheck &rArr; prvIPTimerStart &rArr; vTaskSetTimeOutState
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerStart
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
</UL>

<P><STRONG><a name="[16d]"></a>prvIPTimerReload</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, freertos_ip_timers.o(i.prvIPTimerReload))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvIPTimerReload &rArr; prvIPTimerStart &rArr; vTaskSetTimeOutState
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerStart
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPTimerReload
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vARPTimerReload
</UL>

<P><STRONG><a name="[16c]"></a>prvIPTimerStart</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, freertos_ip_timers.o(i.prvIPTimerStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prvIPTimerStart &rArr; vTaskSetTimeOutState
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSetTimeOutState
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerReload
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIPTimerCheck
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vIPTimerStartARPResolution
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vCheckNetworkTimers
</UL>

<P><STRONG><a name="[1c9]"></a>prvPacketBuffer_to_NetworkBuffer</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, freertos_ip_utils.o(i.prvPacketBuffer_to_NetworkBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvPacketBuffer_to_NetworkBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxUDPPayloadBuffer_to_NetworkBuffer
</UL>

<P><STRONG><a name="[f0]"></a>prvDetermineSocketSize</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, freertos_sockets.o(i.prvDetermineSocketSize))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvDetermineSocketSize
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIPIsNetworkTaskReady
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_socket
</UL>

<P><STRONG><a name="[154]"></a>prvGetPrivatePortNumber</STRONG> (Thumb, 106 bytes, Stack size 32 bytes, freertos_sockets.o(i.prvGetPrivatePortNumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 216 + Unknown Stack Size
<LI>Call Chain = prvGetPrivatePortNumber &rArr; xApplicationGetRandomNumber &rArr; uxRand &rArr; trng_random_range_get &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xApplicationGetRandomNumber
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxListFindListItemWithValue
</UL>
<BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketBind
</UL>

<P><STRONG><a name="[132]"></a>prvTCPCreateStream</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, freertos_sockets.o(i.prvTCPCreateStream))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = prvTCPCreateStream &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPAddRxdata
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendCheck
</UL>

<P><STRONG><a name="[ec]"></a>prvTCPSendCheck</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, freertos_sockets.o(i.prvTCPSendCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = prvTCPSendCheck &rArr; prvTCPCreateStream &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvValidSocket
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateStream
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_send
</UL>

<P><STRONG><a name="[1b1]"></a>prvTCPSetSocketCount</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, freertos_sockets.o(i.prvTCPSetSocketCount))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + In Cycle
<LI>Call Chain = prvTCPSetSocketCount &rArr;  vSocketClose (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
</UL>

<P><STRONG><a name="[d4]"></a>prvValidSocket</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, freertos_sockets.o(i.prvValidSocket))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = prvValidSocket
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_recv
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_listen
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_accept
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendCheck
</UL>

<P><STRONG><a name="[156]"></a>pxListFindListItemWithValue</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, freertos_sockets.o(i.pxListFindListItemWithValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pxListFindListItemWithValue
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xIPIsNetworkTaskReady
</UL>
<BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketBind
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxUDPSocketLookup
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetPrivatePortNumber
</UL>

<P><STRONG><a name="[1a9]"></a>prvTCPNextTimeout</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, freertos_tcp_ip.o(i.prvTCPNextTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = prvTCPNextTimeout &rArr; xTCPWindowTxHasData &rArr; prvTCPWindowTxHasSpace
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowTxHasData
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPSocketCheck
</UL>

<P><STRONG><a name="[1b4]"></a>prvTCPTouchSocket</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, freertos_tcp_ip.o(i.prvTCPTouchSocket))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvTCPTouchSocket
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xProcessReceivedTCPPacket
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
</UL>

<P><STRONG><a name="[196]"></a>prvReadSackOption</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, freertos_tcp_reception.o(i.prvReadSackOption))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = prvReadSackOption &rArr; ulTCPWindowTxSack &rArr; prvTCPWindowTxCheckAck &rArr; prvTCPWindowTxCheckAck_CalcSRTT &rArr; ulTimerGetAge
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxSack
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGet
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulChar2u32
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSingleStepTCPHeaderOptions
</UL>

<P><STRONG><a name="[14b]"></a>prvSingleStepTCPHeaderOptions</STRONG> (Thumb, 326 bytes, Stack size 56 bytes, freertos_tcp_reception.o(i.prvSingleStepTCPHeaderOptions))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = prvSingleStepTCPHeaderOptions &rArr; prvReadSackOption &rArr; ulTCPWindowTxSack &rArr; prvTCPWindowTxCheckAck &rArr; prvTCPWindowTxCheckAck_CalcSRTT &rArr; ulTimerGetAge
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usChar2u16
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReadSackOption
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckOptions
</UL>

<P><STRONG><a name="[157]"></a>prvHandleEstablished</STRONG> (Thumb, 506 bytes, Stack size 96 bytes, freertos_tcp_state_handling.o(i.prvHandleEstablished))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = prvHandleEstablished &rArr; prvTCPPrepareSend &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleFin
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPAddTxData
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxStreamBufferGet
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowTxDone
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowRxEmpty
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxAck
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareSend
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
</UL>

<P><STRONG><a name="[166]"></a>prvHandleSynReceived</STRONG> (Thumb, 398 bytes, Stack size 80 bytes, freertos_tcp_state_handling.o(i.prvHandleSynReceived))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = prvHandleSynReceived &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
</UL>

<P><STRONG><a name="[15c]"></a>prvTCPHandleFin</STRONG> (Thumb, 286 bytes, Stack size 48 bytes, freertos_tcp_state_handling.o(i.prvTCPHandleFin))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = prvTCPHandleFin &rArr; vTCPStateChange &rArr; vSocketCloseNextTime &rArr; vSocketClose &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPStateChange
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleEstablished
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPHandleState
</UL>

<P><STRONG><a name="[163]"></a>prvTCPSocketCopy</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, freertos_tcp_state_handling.o(i.prvTCPSocketCopy))
<BR><BR>[Stack]<UL><LI>Max Depth = 288 + Unknown Stack Size
<LI>Call Chain = prvTCPSocketCopy &rArr; vSocketBind &rArr; prvGetPrivatePortNumber &rArr; xApplicationGetRandomNumber &rArr; uxRand &rArr; trng_random_range_get &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketClose
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vSocketBind
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHandleListen
</UL>

<P><STRONG><a name="[1a7]"></a>prvTCPMakeSurePrepared</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, freertos_tcp_transmission.o(i.prvTCPMakeSurePrepared))
<BR><BR>[Stack]<UL><LI>Max Depth = 288 + Unknown Stack Size
<LI>Call Chain = prvTCPMakeSurePrepared &rArr; prvTCPPrepareConnect &rArr; ulApplicationGetNextSequenceNumber &rArr; uxRand &rArr; trng_random_range_get &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPPrepareConnect
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendPacket
</UL>

<P><STRONG><a name="[1a8]"></a>prvTCPPrepareConnect</STRONG> (Thumb, 412 bytes, Stack size 72 bytes, freertos_tcp_transmission.o(i.prvTCPPrepareConnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 272 + Unknown Stack Size
<LI>Call Chain = prvTCPPrepareConnect &rArr; ulApplicationGetNextSequenceNumber &rArr; uxRand &rArr; trng_random_range_get &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eARPGetCacheEntry
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_OutputARPRequest
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulApplicationGetNextSequenceNumber
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPCreateWindow
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSocketSetMSS
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPMakeSurePrepared
</UL>

<P><STRONG><a name="[1ae]"></a>prvTCPSendSpecialPacketHelper</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, freertos_tcp_transmission.o(i.prvTCPSendSpecialPacketHelper))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = prvTCPSendSpecialPacketHelper &rArr; prvTCPReturnPacket &rArr; xNetworkInterfaceOutput &rArr; vReleaseNetworkBufferAndDescriptor &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPReturnPacket
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendReset
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPSendChallengeAck
</UL>

<P><STRONG><a name="[19e]"></a>prvWinScaleFactor</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, freertos_tcp_transmission.o(i.prvWinScaleFactor))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvWinScaleFactor
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
</UL>
<BR>[Called By]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetSynAckOptions
</UL>

<P><STRONG><a name="[138]"></a>lTCPIncrementTxPosition</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, freertos_tcp_win.o(i.lTCPIncrementTxPosition))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lTCPIncrementTxPosition
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowTxAdd
</UL>

<P><STRONG><a name="[151]"></a>prvCreateSectors</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, freertos_tcp_win.o(i.prvCreateSectors))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = prvCreateSectors &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertFifo
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowCreate
</UL>

<P><STRONG><a name="[1b5]"></a>prvTCPWindowFastRetransmit</STRONG> (Thumb, 148 bytes, Stack size 32 bytes, freertos_tcp_win.o(i.prvTCPWindowFastRetransmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = prvTCPWindowFastRetransmit &rArr; vListInsertFifo
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSequenceLessThan
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertFifo
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxSack
</UL>

<P><STRONG><a name="[134]"></a>prvTCPWindowRx_ExpectedRX</STRONG> (Thumb, 146 bytes, Stack size 40 bytes, freertos_tcp_win.o(i.prvTCPWindowRx_ExpectedRX))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = prvTCPWindowRx_ExpectedRX &rArr; xTCPWindowRxConfirm
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowRxFind
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowRxConfirm
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowFree
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowRxCheck
</UL>

<P><STRONG><a name="[135]"></a>prvTCPWindowRx_UnexpectedRX</STRONG> (Thumb, 240 bytes, Stack size 48 bytes, freertos_tcp_win.o(i.prvTCPWindowRx_UnexpectedRX))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = prvTCPWindowRx_UnexpectedRX &rArr; xTCPWindowNew &rArr; vListInsertFifo
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowRxFind
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowNew
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowRxCheck
</UL>

<P><STRONG><a name="[137]"></a>prvTCPWindowTxAdd_FrontSegment</STRONG> (Thumb, 102 bytes, Stack size 32 bytes, freertos_tcp_win.o(i.prvTCPWindowTxAdd_FrontSegment))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvTCPWindowTxAdd_FrontSegment
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_int32
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowTxAdd
</UL>

<P><STRONG><a name="[1ba]"></a>prvTCPWindowTxCheckAck</STRONG> (Thumb, 284 bytes, Stack size 64 bytes, freertos_tcp_win.o(i.prvTCPWindowTxCheckAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = prvTCPWindowTxCheckAck &rArr; prvTCPWindowTxCheckAck_CalcSRTT &rArr; ulTimerGetAge
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSequenceLessThan
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSequenceGreaterThan
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowFree
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck_CalcSRTT
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxSack
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxAck
</UL>

<P><STRONG><a name="[1bc]"></a>prvTCPWindowTxCheckAck_CalcSRTT</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, freertos_tcp_win.o(i.prvTCPWindowTxCheckAck_CalcSRTT))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvTCPWindowTxCheckAck_CalcSRTT &rArr; ulTimerGetAge
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTimerGetAge
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck
</UL>

<P><STRONG><a name="[1be]"></a>prvTCPWindowTxHasSpace</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, freertos_tcp_win.o(i.prvTCPWindowTxHasSpace))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvTCPWindowTxHasSpace
</UL>
<BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_min_uint32
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowPeekHead
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowTxHasData
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetTXQueue
</UL>

<P><STRONG><a name="[1c6]"></a>pxTCPWindowTx_GetTXQueue</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, freertos_tcp_win.o(i.pxTCPWindowTx_GetTXQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = pxTCPWindowTx_GetTXQueue &rArr; prvTCPWindowTxHasSpace
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowPeekHead
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowGetHead
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxHasSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxGet
</UL>

<P><STRONG><a name="[1c8]"></a>pxTCPWindowTx_GetWaitQueue</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, freertos_tcp_win.o(i.pxTCPWindowTx_GetWaitQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = pxTCPWindowTx_GetWaitQueue &rArr; xTCPWindowGetHead
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowPeekHead
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowGetHead
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTimerGetAge
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxGet
</UL>

<P><STRONG><a name="[1bd]"></a>ulTimerGetAge</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, freertos_tcp_win.o(i.ulTimerGetAge))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ulTimerGetAge
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowTxHasData
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetWaitQueue
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck_CalcSRTT
</UL>

<P><STRONG><a name="[13a]"></a>vListInsertFifo</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, freertos_tcp_win.o(i.vListInsertFifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vListInsertFifo
</UL>
<BR>[Calls]<UL><LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertGeneric
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowNew
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowFree
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowFastRetransmit
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCreateSectors
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxGet
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowTxAdd
</UL>

<P><STRONG><a name="[1e5]"></a>vListInsertGeneric</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, freertos_tcp_win.o(i.vListInsertGeneric))
<BR><BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertFifo
</UL>

<P><STRONG><a name="[1da]"></a>vTCPTimerSet</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, freertos_tcp_win.o(i.vTCPTimerSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vTCPTimerSet
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowNew
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxGet
</UL>

<P><STRONG><a name="[1b8]"></a>vTCPWindowFree</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, freertos_tcp_win.o(i.vTCPWindowFree))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vTCPWindowFree &rArr; vListInsertFifo
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertFifo
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPWindowDestroy
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxCheckAck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowRx_ExpectedRX
</UL>

<P><STRONG><a name="[202]"></a>xSequenceGreaterThanOrEqual</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, freertos_tcp_win.o(i.xSequenceGreaterThanOrEqual))
<BR><BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowRxConfirm
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowRxEmpty
</UL>

<P><STRONG><a name="[1c7]"></a>xTCPWindowGetHead</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, freertos_tcp_win.o(i.xTCPWindowGetHead))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTCPWindowGetHead
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetWaitQueue
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetTXQueue
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTCPWindowTxGet
</UL>

<P><STRONG><a name="[139]"></a>xTCPWindowNew</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, freertos_tcp_win.o(i.xTCPWindowNew))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = xTCPWindowNew &rArr; vListInsertFifo
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTCPTimerSet
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertFifo
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowRx_UnexpectedRX
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lTCPWindowTxAdd
</UL>

<P><STRONG><a name="[1bf]"></a>xTCPWindowPeekHead</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, freertos_tcp_win.o(i.xTCPWindowPeekHead))
<BR><BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTCPWindowTxHasData
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetWaitQueue
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxTCPWindowTx_GetTXQueue
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowTxHasSpace
</UL>

<P><STRONG><a name="[1b7]"></a>xTCPWindowRxConfirm</STRONG> (Thumb, 152 bytes, Stack size 56 bytes, freertos_tcp_win.o(i.xTCPWindowRxConfirm))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = xTCPWindowRxConfirm
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vLoggingPrintf
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSequenceLessThan
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xSequenceGreaterThanOrEqual
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowRx_ExpectedRX
</UL>

<P><STRONG><a name="[1b9]"></a>xTCPWindowRxFind</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, freertos_tcp_win.o(i.xTCPWindowRxFind))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTCPWindowRxFind
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowRx_UnexpectedRX
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTCPWindowRx_ExpectedRX
</UL>

<P><STRONG><a name="[10b]"></a>check_rmii_pins</STRONG> (Thumb, 152 bytes, Stack size 8 bytes, enet.o(i.check_rmii_pins))
<BR><BR>[Stack]<UL><LI>Max Depth = 152 + Unknown Stack Size
<LI>Call Chain = check_rmii_pins &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
</UL>

<P><STRONG><a name="[fb]"></a>enet_gpio_config</STRONG> (Thumb, 408 bytes, Stack size 24 bytes, enet.o(i.enet_gpio_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = enet_gpio_config &rArr; check_rmii_pins &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_ckout0_config
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;syscfg_enet_phy_interface_config
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eth_rmii_gpio_conifg
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_rmii_pins
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitialiseNetwork
</UL>

<P><STRONG><a name="[121]"></a>eth_rmii_gpio_conifg</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, enet.o(i.eth_rmii_gpio_conifg))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = eth_rmii_gpio_conifg &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enet_gpio_config
</UL>

<P><STRONG><a name="[1cc]"></a>trng_config</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, trng.o(i.trng_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = trng_config &rArr; trng_ready_check &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_enable
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_ready_check
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_init
</UL>

<P><STRONG><a name="[68]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
